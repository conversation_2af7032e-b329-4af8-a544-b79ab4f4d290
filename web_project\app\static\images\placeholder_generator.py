#!/usr/bin/env python3
"""
简单的菜品图片占位符生成器
为新增的菜品创建占位符图片
"""

from PIL import Image, ImageDraw, ImageFont
import os

def create_placeholder_image(text, filename, size=(300, 200), bg_color=(240, 240, 240), text_color=(100, 100, 100)):
    """创建占位符图片"""
    # 创建图片
    img = Image.new('RGB', size, bg_color)
    draw = ImageDraw.Draw(img)
    
    # 尝试使用系统字体
    try:
        font = ImageFont.truetype("arial.ttf", 24)
    except:
        try:
            font = ImageFont.truetype("simhei.ttf", 24)  # 中文字体
        except:
            font = ImageFont.load_default()
    
    # 计算文字位置（居中）
    bbox = draw.textbbox((0, 0), text, font=font)
    text_width = bbox[2] - bbox[0]
    text_height = bbox[3] - bbox[1]
    
    x = (size[0] - text_width) // 2
    y = (size[1] - text_height) // 2
    
    # 绘制文字
    draw.text((x, y), text, fill=text_color, font=font)
    
    # 保存图片
    img.save(filename)
    print(f"Created placeholder image: {filename}")

# 创建新菜品的占位符图片
new_dishes = [
    ("麻婆豆腐", "mapo_tofu.png"),
    ("红烧带鱼", "fish2.png"),
    ("蒸蛋羹", "egg_custard.png"),
    ("虾仁炒蛋", "shrimp_egg.png"),
    ("辣椒炒肉", "spicy_pork.png"),
    ("银耳莲子汤", "white_fungus_soup.png")
]

if __name__ == "__main__":
    # 确保images目录存在
    current_dir = os.path.dirname(os.path.abspath(__file__))
    
    for dish_name, filename in new_dishes:
        filepath = os.path.join(current_dir, filename)
        if not os.path.exists(filepath):
            create_placeholder_image(dish_name, filepath)
        else:
            print(f"Image already exists: {filename}")
    
    print("Placeholder image generation completed!")
