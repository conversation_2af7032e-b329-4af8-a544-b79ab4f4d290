#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
更新张建国地址信息脚本
将张建国的地址从"杭州市西湖区西溪路"更新为"浙江省杭州市余杭区龙湖天街1号"
"""

import pymysql

def update_zhangjianguo_address():
    """更新张建国的地址信息"""
    connection = None
    cursor = None
    try:
        # 连接数据库
        connection = pymysql.connect(
            host='localhost',
            user='root',
            password='pqr0713',
            database='elderly_care',
            charset='utf8mb4'
        )
        
        cursor = connection.cursor()
        
        # 查询张建国当前的地址信息
        cursor.execute("SELECT user_id, name, address FROM elderlyuser WHERE user_id = 'E01' OR name = '张建国'")
        current_data = cursor.fetchall()
        
        print("当前张建国的地址信息:")
        for row in current_data:
            print(f"用户ID: {row[0]}, 姓名: {row[1]}, 地址: {row[2]}")
        
        if not current_data:
            print("未找到张建国的记录")
            return
        
        # 更新张建国的地址
        new_address = '浙江省杭州市余杭区龙湖天街1号'
        cursor.execute(
            "UPDATE elderlyuser SET address = %s WHERE user_id = 'E01' OR name = '张建国'",
            (new_address,)
        )
        
        # 同时更新智能手环的GPS位置（如果存在）
        cursor.execute(
            "UPDATE smartwatch SET gps_location = '30.2741,120.1551' WHERE watch_id = 'SW01'"
        )
        
        connection.commit()
        
        # 验证更新结果
        cursor.execute("SELECT user_id, name, address FROM elderlyuser WHERE user_id = 'E01' OR name = '张建国'")
        updated_data = cursor.fetchall()
        
        print("\n更新后张建国的地址信息:")
        for row in updated_data:
            print(f"用户ID: {row[0]}, 姓名: {row[1]}, 地址: {row[2]}")
        
        print(f"\n✅ 成功更新张建国的地址为: {new_address}")
        
    except pymysql.Error as e:
        print(f"❌ 数据库操作失败: {str(e)}")
        if connection:
            connection.rollback()
    except Exception as e:
        print(f"❌ 更新地址失败: {str(e)}")
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

if __name__ == "__main__":
    print("🔄 开始更新张建国的地址信息...")
    update_zhangjianguo_address()
    print("🎉 地址更新操作完成！")
