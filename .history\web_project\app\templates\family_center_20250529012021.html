{% extends "base.html" %}
{% block title %}家属监护中心{% endblock %}
{% block head %}
<link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700;900&family=Noto+Serif+SC:wght@400;600;700&display=swap" rel="stylesheet">
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<link rel="stylesheet" href="{{ url_for('static', filename='css/family_center_styles.css') }}">
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
{% endblock %}

{% block footer %}
<!-- 在家属监护中心不显示页脚 -->
{% endblock %}

{% block content %}
<div id="monitor-app">
    <div class="monitor-layout">
        <div class="monitor-topbar">
            <h1 class="mb-0 fs-4">家属监护中心</h1>
            <div class="top-actions d-flex align-items-center">
                <button class="btn btn-outline-primary">
                    <i class="bi bi-speedometer2"></i> 实时模式
                </button>
                <button class="btn btn-danger">
                    <i class="bi bi-megaphone"></i> 一键呼叫
                </button>
            </div>
        </div>

        <div class="monitor-content-wrapper">
            <aside class="monitor-sidemenu">
                <div class="menu-title"><i class="bi bi-grid-1x2-fill"></i> 监护模块</div>
                <ul>
                    <li :class="{'active': currentMenu === '实时体征看板'}" @click="selectMenu('实时体征看板')">
                        <i class="bi bi-activity"></i> 实时体征看板
                    </li>
                    <li :class="{'active': currentMenu === '实时定位监控'}" @click="selectMenu('实时定位监控')">
                        <i class="bi bi-geo-alt-fill"></i> 实时定位监控
                    </li>
                    <li :class="{'active': currentMenu === '医疗档案管理'}" @click="selectMenu('医疗档案管理')">
                        <i class="bi bi-file-medical"></i> 医疗档案管理
                    </li>
                </ul>
                <div class="menu-title"><i class="bi bi-shield-fill-check"></i> 关怀模块</div>
                <ul>
                    <li :class="{'active': currentMenu === '历史趋势分析'}" @click="selectMenu('历史趋势分析')">
                        <i class="bi bi-graph-up"></i> 历史趋势分析
                    </li>
                    <li :class="{'active': currentMenu === '活动行为分析'}" @click="selectMenu('活动行为分析')">
                        <i class="bi bi-person-lines-fill"></i> 活动行为分析
                    </li>
                    <li :class="{'active': currentMenu === '生活服务代理'}" @click="selectMenu('生活服务代理')">
                        <i class="bi bi-basket-fill"></i> 生活服务代理
                    </li>
                    <li :class="{'active': currentMenu === '应急响应中心'}" @click="selectMenu('应急响应中心')">
                        <i class="bi bi-exclamation-triangle-fill"></i> 应急响应中心
                    </li>
                </ul>
            </aside>

            <div class="monitor-workspace">
                <!-- 删除首页被截图的内容部分 -->

                <section class="monitor-panel" v-if="currentMenu==='历史趋势分析'">
                    <div class="monitor-panel-header">历史趋势分析</div>
                    <div class="monitor-panel-desc">基于长期数据分析健康状况变化趋势，识别潜在风险并生成健康建议。</div>

                    <div class="trend-analysis-container">
                        <div class="trend-chart-box">
                            <h4 class="chart-title">血压与血糖变化趋势（近7天）</h4>
                            <div class="trend-chart-center">
                                <canvas id="trendChart" width="700" height="400"></canvas>
                            </div>
                        </div>
                        <div class="trend-analysis-box">
                            <h4 class="analysis-title">AI健康分析报告</h4>
                            <div class="analysis-content">
                                <p>根据近7天的健康数据分析：</p>
                                <ul>
                                    <li>血压指标总体处于<strong>正常范围</strong>，但波动性略大</li>
                                    <li>血糖值在正常上限附近，有轻微<strong>上升趋势</strong></li>
                                    <li>心率变异性良好，表明心脏功能稳定</li>
                                    <li>活动量略有下降，建议适当增加户外活动</li>
                                </ul>
                                <p>建议：</p>
                                <ol>
                                    <li>保持低盐饮食，控制血压波动</li>
                                    <li>增加蔬菜水果摄入，减少精致碳水化合物</li>
                                    <li>每日固定时间测量血压，记录数据</li>
                                </ol>
                            </div>
                        </div>
                    </div>

                    <div class="risk-tags-container">
                        <div class="risk-tag warning">
                            <i class="bi bi-exclamation-triangle me-2"></i> 血糖波动风险
                        </div>
                        <div class="risk-tag warning">
                            <i class="bi bi-exclamation-triangle me-2"></i> 运动不足风险
                        </div>
                    </div>
                </section>

                <section class="monitor-panel" v-if="currentMenu==='实时体征看板'">
                    <div class="monitor-panel-header"><i class="bi bi-heart-pulse-fill"></i>实时体征看板</div>

                    <!-- 生活监控模块 -->
                    <div class="lifestyle-monitor">
                        <h4><i class="bi bi-activity"></i> 老人生活状态实时监控</h4>
                        <div class="lifestyle-cards">
                            <!-- 运动情况 -->
                            <div class="lifestyle-card">
                                <h5><i class="bi bi-person-walking"></i> 运动情况</h5>
                                <div class="data-row">
                                    <strong>今日步数</strong>
                                    <span class="fw-bold">5,246 / 8,000</span>
                                </div>
                                <div class="progress">
                                    <div class="progress-bar bg-primary" role="progressbar" style="width: 65%" aria-valuenow="65" aria-valuemin="0" aria-valuemax="100"></div>
                                </div>
                                <div class="data-row">
                                    <strong>活动时长</strong>
                                    <span>45 分钟</span>
                                </div>
                                <div class="data-row">
                                    <strong>消耗热量</strong>
                                    <span>156 千卡</span>
                                </div>
                                <div class="card-footer">
                                    <div class="trend-indicator positive">
                                        <i class="bi bi-arrow-up"></i>
                                        <span>较昨日增加 12%</span>
                                    </div>
                                </div>
                            </div>

                            <!-- 睡眠情况 -->
                            <div class="lifestyle-card">
                                <h5><i class="bi bi-moon-stars-fill"></i> 睡眠情况</h5>
                                <div class="data-row">
                                    <strong>昨晚睡眠</strong>
                                    <span class="fw-bold">7.2 小时</span>
                                </div>
                                <div class="progress">
                                    <div class="progress-bar bg-success" role="progressbar" style="width: 80%" aria-valuenow="80" aria-valuemin="0" aria-valuemax="100"></div>
                                </div>
                                <div class="data-row">
                                    <strong>深度睡眠</strong>
                                    <span>2.5 小时</span>
                                </div>
                                <div class="data-row">
                                    <strong>夜间起床</strong>
                                    <span>2 次</span>
                                </div>
                                <div class="card-footer">
                                    <div class="trend-indicator neutral">
                                        <i class="bi bi-dash"></i>
                                        <span>睡眠质量稳定</span>
                                    </div>
                                </div>
                            </div>

                            <!-- 饮食情况 -->
                            <div class="lifestyle-card">
                                <h5><i class="bi bi-cup-hot-fill"></i> 饮食情况</h5>
                                <div class="data-row">
                                    <strong>总热量摄入</strong>
                                    <span class="fw-bold">1,860 千卡</span>
                                </div>
                                <div class="progress">
                                    <div class="progress-bar bg-warning" role="progressbar" style="width: 75%" aria-valuenow="75" aria-valuemin="0" aria-valuemax="100"></div>
                                </div>
                                <div class="data-row">
                                    <strong>蛋白质</strong>
                                    <span>65 克</span>
                                </div>
                                <div class="data-row">
                                    <strong>水分摄入</strong>
                                    <span>1200 毫升</span>
                                </div>
                                <div class="card-footer">
                                    <div class="trend-indicator negative">
                                        <i class="bi bi-arrow-down"></i>
                                        <span>需增加水分摄入</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="vital-signs-summary">
                        <div class="summary-title"><i class="bi bi-clipboard2-pulse-fill me-2"></i>健康状况总结</div>
                        <div class="summary-content">当前老人整体健康状况良好，部分指标（如血糖）存在轻微异常，建议关注饮食与运动。血压、心率等核心指标均在正常范围内。</div>
                    </div>
                    <div class="vital-signs-container">
                        <!-- 第一行：血压、血糖、糖化血红蛋白 -->
                        <div class="vital-signs-row">
                            <div class="vital-sign-card" data-status="warning">
                                <div class="indicator">
                                    <i class="bi bi-heart-pulse-fill text-danger"></i>
                                    血压
                                </div>
                                <div class="value-container">
                                    <span class="value">146/90</span>
                                    <span class="unit">mmHg</span>
                                </div>
                                <div class="last-update">
                                    <i class="bi bi-clock-history"></i>
                                    更新于 10:30
                                </div>
                            </div>
                            <div class="vital-sign-card" data-status="warning">
                                <div class="indicator">
                                    <i class="bi bi-droplet-fill text-primary"></i>
                                    血糖
                                </div>
                                <div class="value-container">
                                    <span class="value">7.6</span>
                                    <span class="trend up">↑</span>
                                    <span class="unit">mmol/L</span>
                                </div>
                                <div class="last-update">
                                    <i class="bi bi-clock-history"></i>
                                    更新于 09:15
                                </div>
                            </div>
                            <div class="vital-sign-card" data-status="warning">
                                <div class="indicator">
                                    <i class="bi bi-eyedropper text-info"></i>
                                    糖化血红蛋白
                                </div>
                                <div class="value-container">
                                    <span class="value">6.9</span>
                                    <span class="unit">%</span>
                                </div>
                                <div class="last-update">
                                    <i class="bi bi-calendar-check"></i>
                                    更新于 2025-05-20
                                </div>
                            </div>
                        </div>

                        <!-- 第二行：胆固醇和低密度脂蛋白 -->
                        <div class="vital-signs-row">
                            <div class="vital-sign-card" data-status="warning">
                                <div class="indicator">
                                    <i class="bi bi-capsule text-warning"></i>
                                    总胆固醇
                                </div>
                                <div class="value-container">
                                    <span class="value">5.5</span>
                                    <span class="unit">mmol/L</span>
                                </div>
                                <div class="last-update">
                                    <i class="bi bi-calendar-check"></i>
                                    更新于 2025-05-20
                                </div>
                            </div>
                            <div class="vital-sign-card" data-status="warning">
                                <div class="indicator">
                                    <i class="bi bi-capsule-pill text-danger"></i>
                                    低密度脂蛋白
                                </div>
                                <div class="value-container">
                                    <span class="value">3.4</span>
                                    <span class="unit">mmol/L</span>
                                </div>
                                <div class="last-update">
                                    <i class="bi bi-calendar-check"></i>
                                    更新于 2025-05-20
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
                <section class="monitor-panel" v-if="currentMenu==='活动行为分析'">
                    <div class="monitor-panel-header"><i class="bi bi-bar-chart-line-fill"></i>活动行为分析</div>
                    <div class="monitor-panel-desc">分析老人社交与活动参与度，发现孤独风险与活跃群体，辅助个性化干预。</div>

                    <div class="activity-analysis-container">
                        <div class="activity-chart-wrapper">
                            <h4 class="chart-title"><i class="bi bi-pie-chart-fill"></i>活动参与度</h4>
                            <div class="activity-chart">
                                <canvas id="activityRadar" height="350"></canvas>
                            </div>
                            <div class="analysis-conclusion">
                                <div class="conclusion-title"><i class="bi bi-lightbulb-fill me-2"></i>结论</div>
                                <p><i class="bi bi-info-circle-fill text-primary me-2"></i>老人在家人聚会中表现积极主动，社区活动和锻炼参与度有显著提升空间。</p>
                                <p><i class="bi bi-lightbulb-fill text-warning me-2"></i>建议：安排每周三的社区健身活动，增强社交互动能力，提高整体活动参与度。</p>
                            </div>
                        </div>

                        <div class="activity-chart-wrapper">
                            <h4 class="chart-title"><i class="bi bi-diagram-3-fill"></i>社交关系分析</h4>
                            <div class="activity-chart">
                                <canvas id="contactGraph" height="350"></canvas>
                            </div>
                            <div class="analysis-conclusion">
                                <div class="conclusion-title"><i class="bi bi-lightbulb-fill me-2"></i>结论</div>
                                <p><i class="bi bi-info-circle-fill text-primary me-2"></i>家庭关系是老人社交网络的核心支柱，但与社区医生和朋友的互动频率明显偏低。</p>
                                <p><i class="bi bi-lightbulb-fill text-warning me-2"></i>建议：促进与社区医生的定期健康交流，协助扩展社交圈层，降低社交孤立风险。</p>
                            </div>
                        </div>
                    </div>

                    <div class="activity-summary">
                        <h5 class="summary-title"><i class="bi bi-clipboard2-data-fill me-2"></i>综合评估</h5>
                        <div class="alert alert-info">
                            <p class="mb-2"><strong>观察结果：</strong>老人整体社交状态良好，但存在社交圈局限性。活动参与多样但频率不足，特别是在社区公共活动方面。</p>
                            <p class="mb-0"><strong>行动建议：</strong>每周至少增加2次有组织的外出活动，重点关注社区健康讲座和老年兴趣小组，丰富社交互动，提高生活质量。</p>
                        </div>
                    </div>
                </section>
                <section class="monitor-panel" v-if="currentMenu==='生活服务代理'">
                    <div class="monitor-panel-header"><i class="bi bi-cup-hot-fill me-2"></i>生活服务代理</div>
                    <div class="monitor-panel-desc">为老人提供多种生活服务，包括远程代订餐、家政服务等</div>

                    <div class="service-container">
                        <div class="service-form-wrapper">
                            <h4 class="service-subtitle mb-3"><i class="bi bi-cup-hot-fill me-2"></i>远程代订餐</h4>
                            <form class="service-form">
                                <div class="form-group">
                                    <label class="form-label"><i class="bi bi-calendar-date me-2"></i>选择日期</label>
                                    <div class="input-wrapper">
                                        <input type="date" class="form-control" value="2025-05-07">
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label class="form-label">餐食类型</label>
                                    <div class="input-wrapper">
                                        <select class="form-select">
                                            <option selected>营养套餐A（低盐低油）</option>
                                            <option>营养套餐B（高蛋白）</option>
                                            <option>普通健康餐</option>
                                            <option>糖尿病专用餐</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label class="form-label">送餐时间</label>
                                    <div class="input-wrapper">
                                        <select class="form-select">
                                            <option>早餐 (07:00-08:00)</option>
                                            <option selected>午餐 (11:30-12:30)</option>
                                            <option>晚餐 (17:30-18:30)</option>
                                        </select>
                                    </div>
                                </div>

                                <button type="button" class="btn-submit"><i class="bi bi-send-fill me-2"></i>提交订餐请求</button>
                            </form>
                        </div>

                        <div class="service-history">
                            <div class="nutrition-advice mb-4">
                                <h5><i class="bi bi-info-circle-fill me-2"></i>营养建议</h5>
                                <p class="mb-0">根据老人健康状况，建议选择低盐低油的营养套餐A，有助于控制血压和血糖。每周可适当选择2-3次高蛋白餐，补充必要营养。</p>
                            </div>

                            <h4 class="history-title"><i class="bi bi-clock-history me-2"></i>历史订餐记录</h4>
                            <div class="history-list">
                                <div class="history-item">
                                    <div class="history-date">2025-05-06</div>
                                    <div class="history-content">
                                        <p><strong>营养套餐A</strong></p>
                                        <p>午餐 (11:30-12:30)</p>
                                        <span class="status-tag done"><i class="bi bi-check-circle me-1"></i>已送达</span>
                                    </div>
                                </div>
                                <div class="history-item">
                                    <div class="history-date">2025-05-05</div>
                                    <div class="history-content">
                                        <p><strong>营养套餐B</strong></p>
                                        <p>午餐 (11:30-12:30)</p>
                                        <span class="status-tag done"><i class="bi bi-check-circle me-1"></i>已送达</span>
                                    </div>
                                </div>
                                <div class="history-item">
                                    <div class="history-date">2025-05-04</div>
                                    <div class="history-content">
                                        <p><strong>营养套餐A</strong></p>
                                        <p>晚餐 (17:30-18:30)</p>
                                        <span class="status-tag done"><i class="bi bi-check-circle me-1"></i>已送达</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
                <section class="monitor-panel" v-if="currentMenu==='实时定位监控'">
                    <div class="monitor-panel-header">实时位置与安全围栏</div>
                    <div class="monitor-panel-desc">可视化展示老人当前位置和活动轨迹，自定义安全电子围栏，异常行为自动预警。</div>

                    <div class="location-container">
                        <!-- 位置信息卡片 -->
                        <div class="location-info-card mb-4">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h5 class="mb-0"><i class="bi bi-geo-alt-fill me-2 text-primary"></i>当前位置信息</h5>
                                <div class="d-flex gap-2">
                                    <!-- 老人切换下拉框 -->
                                    <select class="form-select form-select-sm" v-if="boundElderlyList.length > 1"
                                            v-model="selectedElderlyIndex" @change="switchElderly" style="width: auto;">
                                        <option v-for="(elderly, index) in boundElderlyList" :key="elderly.user_id" :value="index">
                                            [[elderly.name]] [[elderly.is_primary ? '(主)' : '']]
                                        </option>
                                    </select>
                                    <button class="btn btn-sm btn-outline-primary" @click="refreshLocation">
                                        <i class="bi bi-arrow-clockwise me-1"></i> 刷新位置
                                    </button>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="info-item">
                                        <label><i class="bi bi-person-fill me-1"></i>老人姓名：</label>
                                        <span>[[currentLocationInfo.name]]</span>
                                    </div>
                                    <div class="info-item">
                                        <label><i class="bi bi-geo-alt me-1"></i>详细地址：</label>
                                        <span>[[currentLocationInfo.address]]</span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="info-item">
                                        <label><i class="bi bi-clock me-1"></i>更新时间：</label>
                                        <span>[[currentLocationInfo.lastUpdate]]</span>
                                    </div>
                                    <div class="info-item">
                                        <label><i class="bi bi-compass me-1"></i>GPS坐标：</label>
                                        <span>[[currentLocationInfo.coordinates]]</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="map-container">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h4 class="map-title mb-0">实时位置与安全围栏</h4>
                                <div class="fence-controls">
                                    <button class="btn btn-sm btn-outline-primary"><i class="bi bi-plus-circle"></i> 添加围栏</button>
                                    <button class="btn btn-sm btn-outline-secondary"><i class="bi bi-gear"></i> 围栏设置</button>
                                </div>
                            </div>
                            <div class="amap-container" id="amapRealtime"></div>
                            <div class="mt-2 text-muted">
                                <i class="bi bi-info-circle me-1"></i>
                                点击地图标记可查看详细位置信息，蓝色圆圈为500米安全围栏范围
                            </div>
                        </div>

                        <div class="entry-exit-container">
                            <h5 class="mb-3">进出记录</h5>
                            <table class="entry-exit-table">
                                <thead>
                                    <tr>
                                        <th>时间</th>
                                        <th>进出类型</th>
                                        <th>位置</th>
                                        <th>停留时长</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>09:30</td>
                                        <td>进入</td>
                                        <td>家</td>
                                        <td>2小时</td>
                                    </tr>
                                    <tr>
                                        <td>12:00</td>
                                        <td>离开</td>
                                        <td>家</td>
                                        <td>--</td>
                                    </tr>
                                    <tr>
                                        <td>12:15</td>
                                        <td>进入</td>
                                        <td>社区餐厅</td>
                                        <td>45分钟</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </section>
                <section class="monitor-panel" v-if="currentMenu==='应急响应中心'">
                    <div class="monitor-panel-header">应急响应中心</div>
                    <div class="monitor-panel-desc">集中管理所有紧急呼叫与响应流程，支持实时响应与进度跟踪。</div>

                    <!-- 控制面板 -->
                    <div class="emergency-control-panel mb-4">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0"><i class="bi bi-shield-check me-2"></i>紧急事件监控</h5>
                            <div class="d-flex gap-2">
                                <button class="btn btn-sm btn-outline-primary" @click="refreshEmergencyEvents">
                                    <i class="bi bi-arrow-clockwise me-1"></i> 手动刷新
                                </button>
                                <button class="btn btn-sm btn-danger" @click="testEmergencyPopup">
                                    <i class="bi bi-exclamation-triangle me-1"></i> 测试弹窗
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 紧急事件列表 -->
                    <div class="emergency-events-container">
                        <div class="emergency-events-header">
                            <h6 class="mb-3"><i class="bi bi-list-ul me-2"></i>紧急事件列表</h6>
                        </div>

                        <div v-if="emergencyEvents.length === 0" class="alert alert-info">
                            <i class="bi bi-info-circle me-2"></i>
                            暂无紧急事件记录
                        </div>

                        <div v-else class="emergency-events-list">
                            <div v-for="event in emergencyEvents" :key="event.event_id" class="emergency-event-card mb-3">
                                <div class="card">
                                    <div class="card-header d-flex justify-content-between align-items-center">
                                        <div class="d-flex align-items-center">
                                            <i class="bi bi-exclamation-triangle-fill text-danger me-2"></i>
                                            <strong>[[event.elderly_name]] - [[getEmergencyTypeText(event.emergency_type)]]</strong>
                                        </div>
                                        <span class="badge" :class="getStatusBadgeClass(event.status)">
                                            [[event.status]]
                                        </span>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <p class="mb-1"><strong>发生时间：</strong>[[event.created_at]]</p>
                                                <p class="mb-1"><strong>位置：</strong>[[event.address || '位置信息不详']]</p>
                                                <p class="mb-1"><strong>GPS坐标：</strong>[[event.location || '无']]</p>
                                            </div>
                                            <div class="col-md-6">
                                                <p class="mb-1"><strong>家属响应：</strong>
                                                    <span :class="event.family_responded ? 'text-success' : 'text-warning'">
                                                        [[event.family_responded ? '已响应' : '未响应']]
                                                    </span>
                                                </p>
                                                <p class="mb-1"><strong>工作人员响应：</strong>
                                                    <span :class="event.worker_responded ? 'text-success' : 'text-warning'">
                                                        [[event.worker_responded ? '已响应' : '未响应']]
                                                    </span>
                                                </p>
                                            </div>
                                        </div>

                                        <!-- 响应按钮 -->
                                        <div class="mt-3" v-if="event.status === '待处理'">
                                            <button class="btn btn-success me-2" @click="respondToEvent(event.event_id, '我已收到紧急呼叫，正在处理中，请保持冷静。')"
                                                    :disabled="event.family_responded">
                                                <i class="bi bi-check-circle me-1"></i>
                                                [[event.family_responded ? '已响应' : '家属响应']]
                                            </button>
                                            <button class="btn btn-outline-primary" @click="showResponseModal(event)">
                                                <i class="bi bi-chat-dots me-1"></i> 自定义回复
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
                <section class="monitor-panel" v-if="currentMenu==='医疗档案管理'">
                    <div class="monitor-panel-header"><i class="bi bi-file-medical-fill me-2"></i>医疗档案管理</div>
                    <div class="monitor-panel-desc">可追溯全部历史病历、体检报告，AI自动标记重点异常，支持上传/下载与详情查看。</div>

                    <div class="medical-records-header">
                        <div class="medical-records-search">
                            <div class="input-group">
                                <span class="input-group-text"><i class="bi bi-search"></i></span>
                                <input type="text" class="form-control form-control-lg" placeholder="搜索病历、报告..." style="border-radius: 0.5rem 0 0 0.5rem;">
                                <button class="btn btn-primary" style="border-radius: 0 0.5rem 0.5rem 0;"><i class="bi bi-search me-1"></i> 搜索</button>
                            </div>
                            <div class="medical-records-filter mt-3">
                                <button class="filter-btn active"><i class="bi bi-grid-3x3-gap me-1"></i> 全部</button>
                                <button class="filter-btn"><i class="bi bi-journal-medical me-1"></i> 病历</button>
                                <button class="filter-btn"><i class="bi bi-file-earmark-medical me-1"></i> 检查报告</button>
                                <button class="filter-btn"><i class="bi bi-capsule me-1"></i> 用药记录</button>
                            </div>
                        </div>
                        <button class="btn btn-primary upload-btn"><i class="bi bi-upload me-2"></i> 上传新报告</button>
                    </div>

                    <div class="medical-records">
                        <div class="medical-card">
                            <div class="card-header">
                                <div class="card-icon"><i class="bi bi-clipboard2-pulse"></i></div>
                                <div class="card-title-wrapper">
                                    <h3 class="card-title">体检报告</h3>
                                    <span class="card-date"><i class="bi bi-calendar3 me-1"></i>2025-05-20</span>
                                </div>
                            </div>
                            <div class="card-content">
                                <p>年度体检报告，发现血压、血糖偏高，血脂轻度异常。</p>
                                <div class="card-tags">
                                    <span class="card-tag"><i class="bi bi-tag-fill me-1"></i>体检</span>
                                    <span class="card-tag warning"><i class="bi bi-exclamation-triangle-fill me-1"></i>高血压</span>
                                    <span class="card-tag warning"><i class="bi bi-exclamation-triangle-fill me-1"></i>糖尿病</span>
                                </div>
                            </div>
                            <div class="card-actions">
                                <button class="action-btn primary"><i class="bi bi-robot me-1"></i> AI解读</button>
                                <button class="action-btn"><i class="bi bi-download me-1"></i> 下载</button>
                                <button class="action-btn"><i class="bi bi-search me-1"></i> 详情</button>
                            </div>
                        </div>

                        <div class="medical-card">
                            <div class="card-header">
                                <div class="card-icon"><i class="bi bi-hospital"></i></div>
                                <div class="card-title-wrapper">
                                    <h3 class="card-title">门诊记录</h3>
                                    <span class="card-date"><i class="bi bi-calendar3 me-1"></i>2025-05-23</span>
                                </div>
                            </div>
                            <div class="card-content">
                                <p>因体检发现血压、血糖偏高就诊，制定治疗方案。</p>
                                <div class="card-tags">
                                    <span class="card-tag"><i class="bi bi-tag-fill me-1"></i>门诊</span>
                                    <span class="card-tag warning"><i class="bi bi-exclamation-triangle-fill me-1"></i>高血压1级</span>
                                    <span class="card-tag warning"><i class="bi bi-exclamation-triangle-fill me-1"></i>2型糖尿病</span>
                                </div>
                            </div>
                            <div class="card-actions">
                                <button class="action-btn primary"><i class="bi bi-robot me-1"></i> AI解读</button>
                                <button class="action-btn"><i class="bi bi-search me-1"></i> 详情</button>
                            </div>
                        </div>

                        <div class="medical-card">
                            <div class="card-header">
                                <div class="card-icon"><i class="bi bi-eyedropper"></i></div>
                                <div class="card-title-wrapper">
                                    <h3 class="card-title">OGTT检查</h3>
                                    <span class="card-date"><i class="bi bi-calendar3 me-1"></i>2025-05-25</span>
                                </div>
                            </div>
                            <div class="card-content">
                                <p>糖耐量试验确诊2型糖尿病，胰岛素抵抗明显。</p>
                                <div class="card-tags">
                                    <span class="card-tag"><i class="bi bi-tag-fill me-1"></i>检查</span>
                                    <span class="card-tag warning"><i class="bi bi-exclamation-triangle-fill me-1"></i>糖尿病确诊</span>
                                </div>
                            </div>
                            <div class="card-actions">
                                <button class="action-btn"><i class="bi bi-download me-1"></i> 下载</button>
                                <button class="action-btn"><i class="bi bi-search me-1"></i> 详情</button>
                            </div>
                        </div>

                        <div class="medical-card">
                            <div class="card-header">
                                <div class="card-icon"><i class="bi bi-building-fill-add"></i></div>
                                <div class="card-title-wrapper">
                                    <h3 class="card-title">住院记录</h3>
                                    <span class="card-date"><i class="bi bi-calendar3 me-1"></i>2025-06-01 至 2025-06-05</span>
                                </div>
                            </div>
                            <div class="card-content">
                                <p>因血糖波动较大住院调整，建议短期住院调整。</p>
                                <div class="card-tags">
                                    <span class="card-tag"><i class="bi bi-tag-fill me-1"></i>住院</span>
                                    <span class="card-tag warning"><i class="bi bi-exclamation-triangle-fill me-1"></i>血糖波动</span>
                                </div>
                            </div>
                            <div class="card-actions">
                                <button class="action-btn"><i class="bi bi-search me-1"></i> 详情</button>
                                <button class="action-btn"><i class="bi bi-download me-1"></i> 下载</button>
                            </div>
                        </div>
                    </div>
                </section>
                <section class="monitor-panel" v-if="!currentMenu">
                    <div class="monitor-panel-header">欢迎使用家属监护 2.0</div>
                    <div class="text-muted">请选择左侧功能模块，右侧将显示对应的多维度监控与分析视图。支持分屏、四宫格、自由拖拽组合。</div>
                </section>
            </div>
        </div>
    </div>

    <!-- 家属详细回复模态框 - 高z-index确保在紧急呼叫通知模态框上方 -->
    <div class="modal fade" id="responseModal" tabindex="-1" aria-hidden="true" style="z-index: 1060;">
        <div class="modal-dialog modal-lg">
            <div class="modal-content border-primary shadow-lg">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title">
                        <i class="bi bi-chat-heart me-2"></i>详细回复紧急呼叫
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div v-if="selectedEvent" class="emergency-info mb-4">
                        <div class="alert alert-danger">
                            <h6 class="alert-heading">
                                <i class="bi bi-exclamation-triangle-fill me-2"></i>
                                紧急事件信息
                            </h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <p class="mb-1"><strong>老人姓名：</strong>[[selectedEvent.elderly_name]]</p>
                                    <p class="mb-1"><strong>紧急类型：</strong>[[getEmergencyTypeText(selectedEvent.emergency_type)]]</p>
                                </div>
                                <div class="col-md-6">
                                    <p class="mb-1"><strong>发生时间：</strong>[[selectedEvent.created_at]]</p>
                                    <p class="mb-0"><strong>地址：</strong>[[selectedEvent.address || '位置信息获取中...']]</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="response-form">
                        <label for="responseMessage" class="form-label">
                            <i class="bi bi-chat-text me-2"></i>回复消息
                        </label>
                        <textarea
                            class="form-control"
                            id="responseMessage"
                            v-model="responseMessage"
                            rows="5"
                            placeholder="请输入您的详细回复消息，告知老人您的处理方案和预计到达时间..."
                            maxlength="500">
                        </textarea>
                        <div class="form-text">
                            <i class="bi bi-info-circle me-1"></i>
                            建议包含：安抚话语、处理方案、预计到达时间、联系方式等信息
                        </div>
                    </div>

                    <div class="quick-responses mt-3">
                        <label class="form-label">
                            <i class="bi bi-lightning me-2"></i>快速回复模板
                        </label>
                        <div class="d-flex flex-wrap gap-2">
                            <button type="button" class="btn btn-outline-primary btn-sm"
                                    @click="responseMessage = '亲爱的，我已经收到您的紧急呼叫，请不要担心，我正在立即赶往您的位置，预计10-15分钟内到达。请保持冷静，如有需要请拨打120。'">
                                立即前往
                            </button>
                            <button type="button" class="btn btn-outline-primary btn-sm"
                                    @click="responseMessage = '我已经联系了医生和救护车，同时正在赶往现场。请不要移动，保持当前姿势，我们很快就到。有任何不适请立即告诉我。'">
                                医疗协助
                            </button>
                            <button type="button" class="btn btn-outline-primary btn-sm"
                                    @click="responseMessage = '我收到了您的求助信息，正在联系相关人员为您提供帮助。请稍等片刻，我会尽快联系您。如有紧急情况请拨打120。'">
                                联系协助
                            </button>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="bi bi-x-circle me-1"></i>取消
                    </button>
                    <button type="button" class="btn btn-primary" @click="submitResponse" :disabled="!responseMessage.trim()">
                        <i class="bi bi-send me-1"></i>发送回复
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 紧急事件弹窗 -->
    <div class="modal fade" id="emergencyAlertModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content border-danger">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title">
                        <i class="bi bi-exclamation-triangle-fill me-2"></i>紧急事件通知
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body p-0">
                    <div v-if="alertEvent">
                        <!-- 紧急事件头部信息 -->
                        <div class="emergency-header bg-danger text-white p-3">
                            <div class="row align-items-center">
                                <div class="col-md-8">
                                    <h4 class="mb-1">
                                        <i class="bi bi-person-exclamation me-2"></i>
                                        [[alertEvent.elderly_name]] 发起了紧急呼叫
                                    </h4>
                                    <div class="d-flex gap-3 text-white-50">
                                        <span><i class="bi bi-tag-fill me-1"></i>类型：[[getEmergencyTypeText(alertEvent.emergency_type)]]</span>
                                        <span><i class="bi bi-clock-fill me-1"></i>[[alertEvent.created_at]]</span>
                                        <span><i class="bi bi-stopwatch me-1"></i>已持续：[[getElapsedTime(alertEvent.created_at)]]</span>
                                    </div>
                                </div>
                                <div class="col-md-4 text-end">
                                    <div class="emergency-priority-badge">
                                        <i class="bi bi-lightning-fill me-1"></i>
                                        <span class="fw-bold">高优先级</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 主要内容区域 -->
                        <div class="emergency-content p-4">
                            <div class="row">
                                <!-- 左侧：地图和位置信息 -->
                                <div class="col-md-7">
                                    <div class="emergency-map-section">
                                        <h6 class="section-title">
                                            <i class="bi bi-geo-alt-fill text-danger me-2"></i>位置信息
                                        </h6>

                                        <!-- 地图容器 -->
                                        <div class="emergency-map-container mb-3" id="emergencyAlertMap" style="height: 300px; border-radius: 8px; border: 2px solid #dc3545;"></div>

                                        <!-- 位置详情 -->
                                        <div class="location-details">
                                            <div class="row">
                                                <div class="col-6">
                                                    <div class="info-item">
                                                        <label class="text-muted small">详细地址</label>
                                                        <div class="fw-bold">[[alertEvent.address || '获取地址中...']]</div>
                                                    </div>
                                                </div>
                                                <div class="col-6">
                                                    <div class="info-item">
                                                        <label class="text-muted small">GPS坐标</label>
                                                        <div class="fw-bold font-monospace">[[alertEvent.location || '获取中...']]</div>
                                                    </div>
                                                </div>
                                                <div class="col-6 mt-2">
                                                    <div class="info-item">
                                                        <label class="text-muted small">距离您</label>
                                                        <div class="fw-bold text-warning">[[alertEvent.distanceToFamily || '计算中...']]</div>
                                                    </div>
                                                </div>
                                                <div class="col-6 mt-2">
                                                    <div class="info-item">
                                                        <label class="text-muted small">附近资源</label>
                                                        <div class="fw-bold text-info">[[alertEvent.nearbyResources || '查询中...']]</div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 右侧：老人信息和健康状况 -->
                                <div class="col-md-5">
                                    <div class="elderly-info-section">
                                        <h6 class="section-title">
                                            <i class="bi bi-person-fill text-primary me-2"></i>老人信息
                                        </h6>

                                        <div class="elderly-card bg-light p-3 rounded mb-3">
                                            <div class="d-flex align-items-center mb-2">
                                                <div class="elderly-avatar bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 50px; height: 50px;">
                                                    <i class="bi bi-person-fill fs-4"></i>
                                                </div>
                                                <div>
                                                    <h6 class="mb-0">[[alertEvent.elderly_name]]</h6>
                                                    <small class="text-muted">[[alertEvent.elderly_age || '年龄未知']]岁 | [[alertEvent.elderly_gender || '性别未知']]</small>
                                                </div>
                                            </div>
                                            <div class="elderly-contact">
                                                <div class="d-flex justify-content-between">
                                                    <span class="text-muted small">联系电话</span>
                                                    <span class="fw-bold">[[alertEvent.elderly_phone || '未设置']]</span>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- 健康状况摘要 -->
                                        <div class="health-summary mb-3">
                                            <h6 class="section-title">
                                                <i class="bi bi-heart-pulse text-danger me-2"></i>健康状况
                                            </h6>
                                            <div class="health-alerts">
                                                <div class="alert alert-warning py-2 mb-2" v-if="alertEvent.healthRisks">
                                                    <small><i class="bi bi-exclamation-triangle me-1"></i>[[alertEvent.healthRisks]]</small>
                                                </div>
                                                <div class="vital-signs-mini">
                                                    <div class="row text-center">
                                                        <div class="col-4">
                                                            <div class="vital-mini">
                                                                <div class="text-muted small">血压</div>
                                                                <div class="fw-bold small">[[alertEvent.bloodPressure || '--']]</div>
                                                            </div>
                                                        </div>
                                                        <div class="col-4">
                                                            <div class="vital-mini">
                                                                <div class="text-muted small">心率</div>
                                                                <div class="fw-bold small">[[alertEvent.heartRate || '--']]</div>
                                                            </div>
                                                        </div>
                                                        <div class="col-4">
                                                            <div class="vital-mini">
                                                                <div class="text-muted small">血糖</div>
                                                                <div class="fw-bold small">[[alertEvent.bloodSugar || '--']]</div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- 紧急联系人 -->
                                        <div class="emergency-contacts mb-3">
                                            <h6 class="section-title">
                                                <i class="bi bi-telephone-fill text-success me-2"></i>紧急联系人
                                            </h6>
                                            <div class="contact-list">
                                                <div class="contact-item d-flex justify-content-between align-items-center py-1" v-for="contact in alertEvent.emergencyContacts" :key="contact.id">
                                                    <span class="small">[[contact.name]] ([[contact.relation]])</span>
                                                    <a :href="'tel:' + contact.phone" class="btn btn-sm btn-outline-success">
                                                        <i class="bi bi-telephone"></i>
                                                    </a>
                                                </div>
                                            </div>
                                        </div>


                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 底部操作区域 -->
                        <div class="emergency-actions bg-light p-3 border-top">
                            <div class="row align-items-center">
                                <div class="col-md-6">
                                    <div class="response-status">
                                        <small class="text-muted">响应状态：</small>
                                        <span class="badge bg-warning ms-1" v-if="!alertEvent.family_responded && !alertEvent.worker_responded">
                                            <i class="bi bi-clock me-1"></i>等待响应
                                        </span>
                                        <span class="badge bg-success ms-1" v-if="alertEvent.family_responded">
                                            <i class="bi bi-check-circle me-1"></i>家属已响应
                                        </span>
                                        <span class="badge bg-info ms-1" v-if="alertEvent.worker_responded">
                                            <i class="bi bi-check-circle me-1"></i>工作人员已响应
                                        </span>
                                    </div>
                                </div>
                                <div class="col-md-6 text-end">
                                    <div class="action-buttons d-flex gap-2 justify-content-end">
                                        <button class="btn btn-success btn-lg" @click="quickRespond" :disabled="alertEvent.family_responded">
                                            <i class="bi bi-telephone me-2"></i>
                                            [[alertEvent.family_responded ? '已响应' : '立即响应']]
                                        </button>
                                        <button class="btn btn-outline-primary" @click="showDetailedResponse">
                                            <i class="bi bi-chat-dots me-1"></i>详细回复
                                        </button>
                                        <button class="btn btn-outline-secondary" data-bs-dismiss="modal">
                                            <i class="bi bi-clock me-1"></i>稍后处理
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/vue@2.6.14/dist/vue.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="https://webapi.amap.com/maps?v=2.0&key=3ba2467a72df9bf471cbcbde0ebd9ea7"></script>
<script src="{{ url_for('static', filename='js/family_center_vue.js') }}"></script>
{% endblock %}