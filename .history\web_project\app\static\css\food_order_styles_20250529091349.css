/* 订餐系统样式 */
.food-menu-container {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
}

.food-item {
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.3s;
    cursor: pointer;
    box-shadow: 0 3px 10px rgba(0,0,0,0.1);
}

.food-item:hover {
    transform: translateY(-5px) scale(1.02);
    box-shadow: 0 8px 20px rgba(0,0,0,0.15);
}

.food-img-container {
    position: relative;
    height: 180px;
    overflow: hidden;
}

.food-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s;
}

.food-item:hover .food-img {
    transform: scale(1.1);
}

.nutrition-tag {
    display: inline-block;
    font-size: 0.75rem;
    padding: 2px 8px;
    border-radius: 12px;
    margin-right: 5px;
    margin-bottom: 5px;
}

.tag-protein { background-color: #f1c40f; color: #000; }
.tag-fiber { background-color: #2ecc71; color: #fff; }
.tag-vitamin { background-color: #3498db; color: #fff; }
.tag-lowfat { background-color: #9b59b6; color: #fff; }
.tag-lowsugar { background-color: #e74c3c; color: #fff; }

.cart-item {
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
    margin-bottom: 10px;
}

.cart-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.food-preference-item {
    cursor: pointer;
    transition: background-color 0.2s;
    border: 1px solid #eee;
}

.food-preference-item:hover {
    background-color: #f0f0f0;
}
.food-preference-item.active {
    background-color: #0d6efd;
    color: white;
    border-color: #0d6efd;
}

.floating-order-btns {
    position: fixed;
    bottom: 90px; /* Adjusted to be above emergency button */
    right: 20px;
    display: flex;
    flex-direction: column;
    gap: 10px;
    z-index: 999;
}

.order-btn {
    background-color: #28a745;
    color: white;
    border-radius: 50%;
    width: 60px;
    height: 60px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 10px rgba(0,0,0,0.2);
    cursor: pointer;
    transition: all 0.3s;
    text-decoration: none; /* Ensure link-like buttons don't have underlines */
}

.order-btn:hover {
    background-color: #218838;
    transform: scale(1.1);
}

.order-btn span {
    font-size: 0.7rem;
    margin-top: 2px;
}

/* Styles for foodDetailModal and paymentModal (specific to these modals) */
#foodDetailModal .modal-xl {
    max-width: 1200px; /* Or your desired max width */
}

#foodDetailModal .nutrition-info .col-3 div {
    min-height: 70px; /* Ensure consistent height */
    display: flex;
    flex-direction: column;
    justify-content: center;
}

#foodDetailModal .cart-items-container {
    max-height: 300px; /* Or your desired height */
    overflow-y: auto;
}

#paymentModal .payment-options .card {
    cursor: pointer;
}

#paymentModal .payment-options .card:hover {
    background-color: #f8f9fa;
}

#paymentModal .payment-options .form-check-input:checked + .form-check-label .bi-check-circle-fill {
    display: inline-block; /* Show checkmark when selected */
}

#paymentModal .payment-options .form-check-label .bi-check-circle-fill {
    display: none; /* Hide by default */
}

/* 订单状态跟踪样式 */
.order-progress {
    position: relative;
}

.progress-steps {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.step {
    display: flex;
    align-items: flex-start;
    position: relative;
    padding-left: 60px;
    opacity: 0.5;
    transition: all 0.3s ease;
}

.step.active {
    opacity: 1;
}

.step.completed {
    opacity: 1;
}

.step-icon {
    position: absolute;
    left: 0;
    top: 0;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #e9ecef;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    color: #6c757d;
    transition: all 0.3s ease;
}

.step.active .step-icon {
    background: #007bff;
    color: white;
}

.step.completed .step-icon {
    background: #28a745;
    color: white;
}

.step-content h6 {
    margin-bottom: 5px;
    font-weight: 600;
}

.step-content p {
    margin-bottom: 5px;
    font-size: 14px;
}

.timestamp {
    font-size: 12px;
    color: #6c757d;
    font-style: italic;
}

.step.active .timestamp {
    color: #007bff;
    font-weight: 500;
}

.step.completed .timestamp {
    color: #28a745;
    font-weight: 500;
}

/* 连接线 */
.step:not(:last-child)::after {
    content: '';
    position: absolute;
    left: 19px;
    top: 45px;
    width: 2px;
    height: 40px;
    background: #e9ecef;
    transition: all 0.3s ease;
}

.step.completed:not(:last-child)::after {
    background: #28a745;
}

.step.active:not(:last-child)::after {
    background: linear-gradient(to bottom, #007bff 50%, #e9ecef 50%);
}

/* 确认收货按钮动画 */
.step #confirmSection button {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

/* 订单详情表格 */
.order-details .table {
    margin-bottom: 0;
}

.order-details .table td {
    border-top: 1px solid #dee2e6;
    padding: 8px 12px;
}

.order-details .table tfoot td {
    border-top: 2px solid #dee2e6;
    font-weight: 600;
}

/* 工作人员端订单进度条样式 */
.order-progress-worker {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 12px;
    padding: 25px;
    border: 2px solid #dee2e6;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.progress-steps-worker {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    position: relative;
}

.step-worker {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    flex: 1;
    position: relative;
    opacity: 0.4;
    transition: all 0.4s ease;
    transform: scale(0.95);
}

.step-worker.active {
    opacity: 1;
    transform: scale(1.05);
}

.step-worker.completed {
    opacity: 1;
    transform: scale(1);
}

.step-icon-worker {
    width: 55px;
    height: 55px;
    border-radius: 50%;
    background: #e9ecef;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 22px;
    color: #6c757d;
    margin-bottom: 12px;
    transition: all 0.4s ease;
    position: relative;
    z-index: 2;
    border: 3px solid transparent;
    box-shadow: 0 2px 6px rgba(0,0,0,0.1);
}

.step-worker.active .step-icon-worker {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
    transform: scale(1.15);
    border-color: #ffffff;
    box-shadow: 0 4px 12px rgba(0,123,255,0.3);
    animation: pulse-blue 2s infinite;
}

.step-worker.completed .step-icon-worker {
    background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
    color: white;
    border-color: #ffffff;
    box-shadow: 0 3px 10px rgba(40,167,69,0.3);
}

@keyframes pulse-blue {
    0% { box-shadow: 0 4px 12px rgba(0,123,255,0.3); }
    50% { box-shadow: 0 6px 16px rgba(0,123,255,0.5); }
    100% { box-shadow: 0 4px 12px rgba(0,123,255,0.3); }
}

.step-content-worker h6 {
    margin-bottom: 6px;
    font-size: 14px;
    font-weight: 700;
    transition: all 0.3s ease;
}

.step-content-worker small {
    font-size: 12px;
    line-height: 1.3;
    font-weight: 500;
}

.step-worker.active .step-content-worker h6 {
    color: #007bff;
    text-shadow: 0 1px 2px rgba(0,123,255,0.2);
}

.step-worker.completed .step-content-worker h6 {
    color: #28a745;
    text-shadow: 0 1px 2px rgba(40,167,69,0.2);
}

/* 增强的连接线 */
.step-worker:not(:last-child)::after {
    content: '';
    position: absolute;
    top: 27px;
    left: 60%;
    right: -40%;
    height: 4px;
    background: #e9ecef;
    z-index: 1;
    border-radius: 2px;
    transition: all 0.4s ease;
}

.step-worker.completed:not(:last-child)::after {
    background: linear-gradient(to right, #28a745 0%, #20c997 100%);
    box-shadow: 0 1px 3px rgba(40,167,69,0.3);
}

.step-worker.active:not(:last-child)::after {
    background: linear-gradient(to right, #007bff 50%, #e9ecef 50%);
    box-shadow: 0 1px 3px rgba(0,123,255,0.3);
    animation: progress-flow 2s ease-in-out infinite;
}

@keyframes progress-flow {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* 我的订单模态窗口样式 */
.order-card {
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid #e9ecef;
}

.order-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    border-color: #007bff;
}

.order-card h6 {
    color: #6c757d;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    margin-bottom: 5px;
}

.order-card p {
    font-size: 14px;
    margin-bottom: 0;
}

.order-card .badge {
    font-size: 12px;
    padding: 6px 12px;
}