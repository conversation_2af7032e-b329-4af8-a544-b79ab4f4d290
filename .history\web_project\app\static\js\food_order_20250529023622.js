const foodOrderModule = {
    data: {
        cart: {
            items: [],
            totalPrice: 0
        },
        currentFood: null,
        foodDetailModalInstance: null,
        paymentModalInstance: null,
        orderStatusModalInstance: null,
        myOrdersModalInstance: null,
        currentOrderId: null,
        orderStatusTimer: null,
        myOrders: [], // 存储用户的订单列表
        foodList: [
            {
                id: 1,
                name: "清蒸鲈鱼",
                price: 28,
                image: "/static/images/fish.png",
                tags: ["高蛋白", "低脂"],
                info: "适合高血压人群",
                badge: "医生推荐",
                badgeClass: "bg-success",
                category: "recommended",
                spicyLevel: 0, // 0-不辣, 1-微辣, 2-中辣, 3-重辣
                allergens: [], // 过敏原
                dietaryTags: ["low_salt", "high_protein", "heart_healthy"],
                nutrition: {
                    protein: 20,
                    fat: 5,
                    carbs: 10,
                    salt: 2
                },
                benefits: [
                    "富含DHA，有助于心脑血管健康",
                    "清蒸烹饪法，低脂健康",
                    "优质蛋白质来源，易于消化吸收"
                ]
            },
            {
                id: 2,
                name: "五谷杂粮粥",
                price: 15,
                image: "/static/images/wugu.png",
                tags: ["高纤维", "低糖"],
                info: "促进肠道健康",
                badge: "营养均衡",
                badgeClass: "bg-primary",
                category: "recommended",
                spicyLevel: 0,
                allergens: [],
                dietaryTags: ["low_sugar", "high_fiber", "digestive_health"],
                nutrition: {
                    protein: 8,
                    fat: 2,
                    carbs: 30,
                    salt: 1
                },
                benefits: [
                    "富含膳食纤维，促进肠道蠕动",
                    "多种谷物营养均衡",
                    "低糖设计，适合血糖控制"
                ]
            },
            {
                id: 3,
                name: "西兰花炒牛肉",
                price: 25,
                image: "/static/images/beaf.png",
                tags: ["高蛋白", "维生素"],
                info: "增强免疫力",
                category: "recommended",
                spicyLevel: 0,
                allergens: [],
                dietaryTags: ["high_protein", "vitamin_rich", "immune_boost"],
                nutrition: {
                    protein: 18,
                    fat: 8,
                    carbs: 15,
                    salt: 2.5
                },
                benefits: [
                    "牛肉富含优质蛋白和铁质",
                    "西兰花含丰富维生素C和抗氧化物",
                    "搭配科学，营养均衡"
                ]
            },
            {
                id: 4,
                name: "三文鱼沙拉",
                price: 32,
                image: "/static/images/sala.png",
                tags: ["优质脂肪", "维生素"],
                info: "心脑血管健康",
                category: "recommended",
                spicyLevel: 0,
                allergens: ["seafood"],
                dietaryTags: ["omega3", "heart_healthy", "low_carb"],
                nutrition: {
                    protein: 15,
                    fat: 12,
                    carbs: 10,
                    salt: 1.5
                },
                benefits: [
                    "富含优质Omega-3脂肪酸",
                    "新鲜蔬菜提供充足维生素",
                    "有助于降低胆固醇，保护心血管健康"
                ]
            },
            {
                id: 5,
                name: "山药排骨汤",
                price: 36,
                image: "/static/images/shanyao.png",
                tags: ["滋补", "易消化"],
                info: "健脾益胃",
                badge: "老年养生",
                badgeClass: "bg-warning",
                category: "recommended",
                spicyLevel: 0,
                allergens: [],
                dietaryTags: ["digestive_health", "nourishing", "calcium_rich"],
                nutrition: {
                    protein: 18,
                    fat: 10,
                    carbs: 25,
                    salt: 2.5
                },
                benefits: [
                    "含丰富的维生素和矿物质",
                    "山药易于消化吸收，养胃健脾",
                    "排骨提供丰富蛋白质和钙质"
                ]
            },
            {
                id: 6,
                name: "清蒸南瓜",
                price: 18,
                image: "/static/images/nangua.png",
                tags: ["高纤维", "低糖"],
                info: "适合糖尿病人",
                badge: "低糖食谱",
                badgeClass: "bg-info",
                category: "diabetic",
                spicyLevel: 0,
                allergens: [],
                dietaryTags: ["low_sugar", "high_fiber", "diabetic_friendly"],
                nutrition: {
                    protein: 5,
                    fat: 1,
                    carbs: 20,
                    salt: 0.5
                },
                benefits: [
                    "低糖低脂，适合糖尿病患者",
                    "富含胡萝卜素和膳食纤维",
                    "帮助控制血糖，促进肠道健康"
                ]
            },
            // 新增菜品
            {
                id: 7,
                name: "蒸蛋羹",
                price: 12,
                image: "/static/images/dish.png",
                tags: ["高蛋白", "易消化"],
                info: "老年人最爱",
                badge: "温和营养",
                badgeClass: "bg-success",
                category: "lowsalt",
                spicyLevel: 0,
                allergens: ["egg"],
                dietaryTags: ["low_salt", "high_protein", "easy_digest"],
                nutrition: {
                    protein: 12,
                    fat: 8,
                    carbs: 5,
                    salt: 0.8
                },
                benefits: [
                    "蛋白质含量高，易于吸收",
                    "质地柔软，适合咀嚼困难的老人",
                    "含有丰富的卵磷脂，有益大脑健康"
                ]
            },
            {
                id: 8,
                name: "银耳莲子汤",
                price: 16,
                image: "/static/images/yiner.png",
                tags: ["滋阴", "润燥"],
                info: "美容养颜",
                badge: "养生汤品",
                badgeClass: "bg-info",
                category: "recommended",
                spicyLevel: 0,
                allergens: [],
                dietaryTags: ["low_sugar", "nourishing", "beauty"],
                nutrition: {
                    protein: 4,
                    fat: 1,
                    carbs: 18,
                    salt: 0.3
                },
                benefits: [
                    "银耳富含胶质，滋润肌肤",
                    "莲子安神养心，改善睡眠",
                    "低热量，适合控制体重"
                ]
            },
            {
                id: 9,
                name: "番茄鸡蛋面",
                price: 20,
                image: "/static/images/tomato_noodle.png",
                tags: ["维生素", "易消化"],
                info: "经典搭配",
                category: "recommended",
                spicyLevel: 0,
                allergens: ["egg", "gluten"],
                dietaryTags: ["vitamin_rich", "comfort_food"],
                nutrition: {
                    protein: 14,
                    fat: 6,
                    carbs: 35,
                    salt: 2.2
                },
                benefits: [
                    "番茄富含番茄红素，抗氧化",
                    "鸡蛋提供优质蛋白质",
                    "面条易消化，提供能量"
                ]
            },
            {
                id: 10,
                name: "冬瓜排骨汤",
                price: 24,
                image: "/static/images/donggua.png",
                tags: ["清热", "利水"],
                info: "夏季消暑",
                category: "lowsalt",
                spicyLevel: 0,
                allergens: [],
                dietaryTags: ["low_salt", "cooling", "diuretic"],
                nutrition: {
                    protein: 16,
                    fat: 8,
                    carbs: 12,
                    salt: 1.8
                },
                benefits: [
                    "冬瓜清热利水，消除水肿",
                    "排骨补充蛋白质和钙质",
                    "汤品温和，适合夏季食用"
                ]
            },
            {
                id: 11,
                name: "白萝卜炖牛腩",
                price: 30,
                image: "/static/images/luobo.png",
                tags: ["温补", "易消化"],
                info: "冬季暖胃",
                category: "recommended",
                spicyLevel: 0,
                allergens: [],
                dietaryTags: ["warming", "digestive_health", "iron_rich"],
                nutrition: {
                    protein: 20,
                    fat: 12,
                    carbs: 15,
                    salt: 2.3
                },
                benefits: [
                    "牛腩富含蛋白质和铁质",
                    "白萝卜助消化，清热化痰",
                    "炖煮软烂，适合老年人咀嚼"
                ]
            },
            {
                id: 12,
                name: "蒸蛋羹配虾仁",
                price: 22,
                image: "/static/images/shrimp_egg.png",
                tags: ["高蛋白", "鲜美"],
                info: "营养丰富",
                category: "recommended",
                spicyLevel: 0,
                allergens: ["egg", "seafood"],
                dietaryTags: ["high_protein", "calcium_rich", "easy_digest"],
                nutrition: {
                    protein: 18,
                    fat: 10,
                    carbs: 6,
                    salt: 1.5
                },
                benefits: [
                    "虾仁富含优质蛋白和钙质",
                    "蒸蛋嫩滑，易于消化",
                    "搭配营养均衡，口感丰富"
                ]
            },
            {
                id: 13,
                name: "小米粥配咸菜",
                price: 10,
                image: "/static/images/xiaomi.png",
                tags: ["养胃", "清淡"],
                info: "传统搭配",
                category: "lowsalt",
                spicyLevel: 0,
                allergens: [],
                dietaryTags: ["low_salt", "digestive_health", "traditional"],
                nutrition: {
                    protein: 6,
                    fat: 2,
                    carbs: 25,
                    salt: 1.2
                },
                benefits: [
                    "小米养胃健脾，易于消化",
                    "咸菜开胃，促进食欲",
                    "经济实惠，营养温和"
                ]
            },
            {
                id: 14,
                name: "蒸蛋羹配肉末",
                price: 18,
                image: "/static/images/meat_egg.png",
                tags: ["高蛋白", "嫩滑"],
                info: "荤素搭配",
                category: "recommended",
                spicyLevel: 0,
                allergens: ["egg"],
                dietaryTags: ["high_protein", "iron_rich", "easy_digest"],
                nutrition: {
                    protein: 16,
                    fat: 12,
                    carbs: 4,
                    salt: 1.8
                },
                benefits: [
                    "肉末提供丰富蛋白质和铁质",
                    "蒸蛋嫩滑，口感温和",
                    "营养密度高，适合体弱老人"
                ]
            },
            {
                id: 15,
                name: "紫菜蛋花汤",
                price: 14,
                image: "/static/images/zicai.png",
                tags: ["清淡", "补碘"],
                info: "海洋营养",
                category: "lowsalt",
                spicyLevel: 0,
                allergens: ["egg"],
                dietaryTags: ["low_salt", "iodine_rich", "light"],
                nutrition: {
                    protein: 8,
                    fat: 4,
                    carbs: 6,
                    salt: 1.0
                },
                benefits: [
                    "紫菜富含碘元素，有益甲状腺",
                    "蛋花提供优质蛋白质",
                    "汤品清淡，不增加肠胃负担"
                ]
            },
            {
                id: 16,
                name: "红烧豆腐",
                price: 16,
                image: "/static/images/doufu.png",
                tags: ["植物蛋白", "钙质"],
                info: "素食营养",
                category: "diabetic",
                spicyLevel: 1,
                allergens: ["soy"],
                dietaryTags: ["plant_protein", "calcium_rich", "low_sugar"],
                nutrition: {
                    protein: 12,
                    fat: 8,
                    carbs: 10,
                    salt: 2.0
                },
                benefits: [
                    "豆腐富含植物蛋白和钙质",
                    "大豆异黄酮有益女性健康",
                    "低热量，适合控制体重"
                ]
            },
            {
                id: 17,
                name: "莲藕排骨汤",
                price: 28,
                image: "/static/images/liannou.png",
                tags: ["清热", "补血"],
                info: "秋季养生",
                category: "recommended",
                spicyLevel: 0,
                allergens: [],
                dietaryTags: ["cooling", "blood_nourishing", "seasonal"],
                nutrition: {
                    protein: 17,
                    fat: 9,
                    carbs: 20,
                    salt: 2.1
                },
                benefits: [
                    "莲藕清热凉血，润燥生津",
                    "排骨补充蛋白质和钙质",
                    "秋季食用，润燥养肺"
                ]
            },
            {
                id: 18,
                name: "蒸蛋羹配青菜",
                price: 15,
                image: "/static/images/vegetable_egg.png",
                tags: ["维生素", "清淡"],
                info: "素食搭配",
                category: "lowsalt",
                spicyLevel: 0,
                allergens: ["egg"],
                dietaryTags: ["low_salt", "vitamin_rich", "vegetarian"],
                nutrition: {
                    protein: 10,
                    fat: 6,
                    carbs: 8,
                    salt: 0.9
                },
                benefits: [
                    "青菜富含维生素和纤维",
                    "蒸蛋提供优质蛋白质",
                    "搭配清淡，营养均衡"
                ]
            }
        ],
        // 筛选状态
        filterState: {
            category: 'all', // 当前选中的营养计划
            noSpicy: false,
            noSeafood: false,
            lowSalt: false,
            lowSugar: false,
            easyDigest: false
        },
        // 筛选后的菜品列表
        filteredFoodList: []
    },
    methods: {
        // 筛选相关方法
        applyFilters() {
            let filtered = [...this.foodList];

            // 按营养计划筛选
            if (this.filterState.category !== 'all') {
                filtered = filtered.filter(food => food.category === this.filterState.category);
            }

            // 按忌口条件筛选
            if (this.filterState.noSpicy) {
                filtered = filtered.filter(food => food.spicyLevel === 0);
            }

            if (this.filterState.noSeafood) {
                filtered = filtered.filter(food => !food.allergens.includes('seafood'));
            }

            if (this.filterState.lowSalt) {
                filtered = filtered.filter(food => food.dietaryTags.includes('low_salt') || food.nutrition.salt <= 1.5);
            }

            if (this.filterState.lowSugar) {
                filtered = filtered.filter(food => food.dietaryTags.includes('low_sugar') || food.dietaryTags.includes('diabetic_friendly'));
            }

            if (this.filterState.easyDigest) {
                filtered = filtered.filter(food => food.dietaryTags.includes('easy_digest'));
            }

            this.filteredFoodList = filtered;
            return filtered;
        },

        // 切换营养计划
        selectCategory(category) {
            this.filterState.category = category;
            this.applyFilters();
            this.updateFoodDisplay();
        },

        // 切换忌口筛选
        toggleFilter(filterType) {
            this.filterState[filterType] = !this.filterState[filterType];
            this.applyFilters();
            this.updateFoodDisplay();
        },

        // 清除所有筛选
        clearAllFilters() {
            this.filterState = {
                category: 'all',
                noSpicy: false,
                noSeafood: false,
                lowSalt: false,
                lowSugar: false,
                easyDigest: false
            };
            this.applyFilters();
            this.updateFoodDisplay();
        },

        // 更新菜品显示
        updateFoodDisplay() {
            const container = document.querySelector('.food-menu-container');
            if (!container) return;

            const foodsToShow = this.filteredFoodList.length > 0 ? this.filteredFoodList : this.foodList;

            // 生成菜品HTML
            let html = '';
            foodsToShow.forEach(food => {
                const badgeHtml = food.badge ? `<span class="position-absolute top-0 end-0 badge ${food.badgeClass} m-2 fs-6 p-2">${food.badge}</span>` : '';

                html += `
                    <div class="food-item" onclick="window.foodOrderManager.showFoodDetail(${food.id})">
                        <div class="food-img-container">
                            <img src="${food.image}" class="food-img" alt="${food.name}">
                            ${badgeHtml}
                        </div>
                        <div class="p-3">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <h4 class="mb-0 fw-bold">${food.name}</h4>
                                <button class="btn btn-sm btn-outline-primary rounded-circle">
                                    <i class="bi bi-volume-up-fill fs-5"></i>
                                </button>
                            </div>
                            <div class="mb-2">
                                ${food.tags.map(tag => `<span class="nutrition-tag tag-protein fs-6">${tag}</span>`).join('')}
                            </div>
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <span class="fw-bold fs-4 me-2">¥${food.price}</span>
                                    <button class="btn btn-primary btn-lg" onclick="event.stopPropagation(); window.foodOrderManager.addToCart(${food.id})">
                                        <i class="bi bi-plus-lg"></i> 加入
                                    </button>
                                </div>
                                <span class="text-success fs-6">${food.info}</span>
                            </div>
                        </div>
                    </div>
                `;
            });

            container.innerHTML = html;

            // 更新筛选结果计数
            this.updateFilterCount(foodsToShow.length);
        },

        // 更新筛选结果计数
        updateFilterCount(count) {
            const countElement = document.querySelector('.filter-count');
            if (countElement) {
                countElement.textContent = `共找到 ${count} 道菜品`;
            }
        },

        // 获取当前显示的菜品列表
        getCurrentFoodList() {
            return this.filteredFoodList.length > 0 ? this.filteredFoodList : this.foodList;
        },

        addToCart(foodId) {
            const food = this.foodList.find(f => f.id === foodId);
            if (!food) return;

            const existingItem = this.cart.items.find(item => item.id === foodId);

            if (existingItem) {
                existingItem.quantity += 1;
            } else {
                this.cart.items.push({
                    id: food.id,
                    name: food.name,
                    price: food.price,
                    image: food.image,
                    quantity: 1,
                    tags: food.tags
                });
            }
            this.updateCartTotal();
            this.showFoodDetail(foodId);
        },
        updateCartTotal() {
            this.cart.totalPrice = this.cart.items.reduce((total, item) => {
                return total + (item.price * item.quantity);
            }, 0);
        },
        showFoodDetail(foodId) {
            this.currentFood = this.foodList.find(f => f.id === foodId);
            if (this.foodDetailModalInstance) {
                this.foodDetailModalInstance.show();
            } else {
                console.error("foodDetailModalInstance not initialized. Modal cannot be shown.");
            }
        },
        increaseQuantity(itemId) {
            const item = this.cart.items.find(i => i.id === itemId);
            if (item) {
                item.quantity += 1;
                this.updateCartTotal();
            }
        },
        decreaseQuantity(itemId) {
            const item = this.cart.items.find(i => i.id === itemId);
            if (item && item.quantity > 1) {
                item.quantity -= 1;
                this.updateCartTotal();
            } else if (item && item.quantity === 1) {
                this.removeFromCart(itemId);
            }
        },
        removeFromCart(itemId) {
            const index = this.cart.items.findIndex(i => i.id === itemId);
            if (index !== -1) {
                this.cart.items.splice(index, 1);
                this.updateCartTotal();
            }
        },
        checkout() {
            if (this.paymentModalInstance) {
                this.paymentModalInstance.show();
            } else {
                console.error("paymentModalInstance not initialized. Payment modal cannot be shown.");
            }
            if (this.foodDetailModalInstance) {
                this.foodDetailModalInstance.hide();
            }
        },
        async confirmPayment() {
            try {
                // 获取当前登录用户ID
                let currentUserId = 'elderly_001'; // 默认值

                try {
                    const userResponse = await fetch('/api/auth/current_user');
                    if (userResponse.ok) {
                        const userData = await userResponse.json();
                        if (userData.logged_in && userData.user_type === 'elderly') {
                            currentUserId = userData.user_id;
                        }
                    }
                } catch (error) {
                    console.warn('获取当前用户信息失败，使用默认用户ID:', error);
                    // 尝试从URL参数获取
                    const urlParams = new URLSearchParams(window.location.search);
                    const urlUserId = urlParams.get('user_id');
                    if (urlUserId) {
                        currentUserId = urlUserId;
                    }
                }

                console.log('使用用户ID提交订单:', currentUserId);

                // 收集订单信息
                const orderData = {
                    user_id: currentUserId, // 从登录信息或URL参数获取用户ID
                    items: this.cart.items.map(item => ({
                        id: item.id,
                        name: item.name,
                        price: item.price,
                        quantity: item.quantity,
                        tags: item.tags
                    })),
                    total_price: this.cart.totalPrice,
                    delivery_fee: 5,
                    delivery_time: this.getSelectedDeliveryTime(),
                    address: this.getDeliveryAddress(),
                    payment_method: '子女代付',
                    special_notes: this.getSpecialNotes()
                };

                // 发送订单到后端
                const response = await fetch('/api/food_orders/submit', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(orderData)
                });

                const result = await response.json();

                if (response.ok && result.success) {
                    // 保存当前订单ID
                    this.currentOrderId = result.order_id;

                    // 显示订单状态跟踪模态窗口
                    this.showOrderStatusModal(result.order);

                    // 清空购物车
                    this.cart.items = [];
                    this.updateCartTotal();

                    // 关闭支付模态框
                    if (this.paymentModalInstance) {
                        this.paymentModalInstance.hide();
                    }

                    console.log('订单提交成功:', result);
                } else {
                    throw new Error(result.error || '订单提交失败');
                }

            } catch (error) {
                console.error('订单提交失败:', error);
                alert(`订单提交失败: ${error.message}\n请稍后重试或联系客服。`);
            }
        },

        // 获取选中的配送时间
        getSelectedDeliveryTime() {
            const timeSelect = document.querySelector('#paymentModal select');
            if (timeSelect && timeSelect.value) {
                return timeSelect.options[timeSelect.selectedIndex].text;
            }
            return '11:30-12:00'; // 默认时间
        },

        // 获取配送地址
        getDeliveryAddress() {
            // 这里可以从用户设置或表单中获取，暂时使用默认地址
            return '浙江省杭州市余杭区龙湖天街1号';
        },

        // 获取特殊备注
        getSpecialNotes() {
            // 这里可以从表单中获取用户输入的备注
            return '请轻敲门，老人听力不太好';
        },

        // 显示订单状态跟踪模态窗口
        showOrderStatusModal(order) {
            // 填充订单基本信息
            document.getElementById('orderNumber').textContent = order.order_id;
            document.getElementById('submitTime').textContent = this.formatTime(new Date());

            // 填充订单详情
            this.populateOrderDetails(order);

            // 显示模态窗口
            if (this.orderStatusModalInstance) {
                this.orderStatusModalInstance.show();
            }

            // 开始轮询订单状态
            this.startOrderStatusPolling();
        },

        // 填充订单详情
        populateOrderDetails(order) {
            const orderItemsList = document.getElementById('orderItemsList');
            const orderTotal = document.getElementById('orderTotal');

            // 清空现有内容
            orderItemsList.innerHTML = '';

            // 添加订单项目
            order.items.forEach(item => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${item.name} x${item.quantity}</td>
                    <td class="text-end">¥${(item.price * item.quantity).toFixed(2)}</td>
                `;
                orderItemsList.appendChild(row);
            });

            // 添加配送费
            const deliveryRow = document.createElement('tr');
            deliveryRow.innerHTML = `
                <td>配送费</td>
                <td class="text-end">¥${order.delivery_fee.toFixed(2)}</td>
            `;
            orderItemsList.appendChild(deliveryRow);

            // 设置总价
            orderTotal.textContent = `¥${order.final_price.toFixed(2)}`;
        },

        // 开始轮询订单状态
        startOrderStatusPolling() {
            // 清除现有定时器
            if (this.orderStatusTimer) {
                clearInterval(this.orderStatusTimer);
            }

            // 每5秒检查一次订单状态
            this.orderStatusTimer = setInterval(() => {
                this.checkOrderStatus();
            }, 5000);
        },

        // 检查订单状态
        async checkOrderStatus() {
            if (!this.currentOrderId) return;

            try {
                const response = await fetch(`/api/food_orders/status/${this.currentOrderId}`);
                const result = await response.json();

                if (response.ok && result.success) {
                    this.updateOrderProgress(result.order);
                }
            } catch (error) {
                console.error('检查订单状态失败:', error);
            }
        },

        // 更新订单进度
        updateOrderProgress(order) {
            const steps = ['step1', 'step2', 'step3', 'step4', 'step5'];

            // 重置所有步骤
            steps.forEach(stepId => {
                const step = document.getElementById(stepId);
                step.classList.remove('active', 'completed');
            });

            // 根据订单状态更新进度
            switch(order.status) {
                case 'pending_assignment':
                    this.activateStep('step1');
                    break;
                case 'assigned':
                    this.completeStep('step1');
                    this.activateStep('step2');
                    this.updateDeliveryInfo(order);
                    // 模拟配送过程（15秒后自动变为配送中）
                    setTimeout(() => {
                        this.simulateDelivery();
                    }, 15000);
                    break;
                case 'delivering':
                    this.completeStep('step1');
                    this.completeStep('step2');
                    this.activateStep('step3');
                    this.updateDeliveryProgress();
                    // 模拟送达（10秒后自动变为已送达）
                    setTimeout(() => {
                        this.simulateArrival();
                    }, 10000);
                    break;
                case 'delivered':
                    this.completeStep('step1');
                    this.completeStep('step2');
                    this.completeStep('step3');
                    this.activateStep('step4');
                    this.showConfirmSection();
                    break;
                case 'completed':
                    this.completeStep('step1');
                    this.completeStep('step2');
                    this.completeStep('step3');
                    this.completeStep('step4');
                    this.completeStep('step5');
                    this.hideConfirmSection();
                    console.log('订单已完成，所有步骤已标记为完成');
                    // 停止轮询
                    if (this.orderStatusTimer) {
                        clearInterval(this.orderStatusTimer);
                        this.orderStatusTimer = null;
                    }
                    break;
            }
        },

        // 激活步骤
        activateStep(stepId) {
            const step = document.getElementById(stepId);
            step.classList.add('active');
        },

        // 完成步骤
        completeStep(stepId) {
            const step = document.getElementById(stepId);
            step.classList.remove('active');
            step.classList.add('completed');
        },

        // 更新配送信息
        updateDeliveryInfo(order) {
            const deliveryInfo = document.getElementById('deliveryInfo');
            const assignTime = document.getElementById('assignTime');

            deliveryInfo.textContent = `配送员：${order.assigned_staff_name}，预计15-25分钟内到达`;
            assignTime.textContent = this.formatTime(new Date());
        },

        // 更新配送进度
        updateDeliveryProgress() {
            const deliveryProgress = document.getElementById('deliveryProgress');
            const deliveryTime = document.getElementById('deliveryTime');

            deliveryProgress.textContent = '配送员正在路上，预计10分钟内到达';
            deliveryTime.textContent = this.formatTime(new Date());
        },

        // 显示确认收货按钮
        showConfirmSection() {
            const confirmSection = document.getElementById('confirmSection');
            const arrivalTime = document.getElementById('arrivalTime');

            confirmSection.style.display = 'block';
            arrivalTime.textContent = this.formatTime(new Date());
        },

        // 隐藏确认收货按钮
        hideConfirmSection() {
            const confirmSection = document.getElementById('confirmSection');
            const completeTime = document.getElementById('completeTime');

            confirmSection.style.display = 'none';
            completeTime.textContent = this.formatTime(new Date());
        },

        // 模拟配送过程
        async simulateDelivery() {
            if (!this.currentOrderId) return;

            try {
                await fetch(`/api/food_orders/simulate_delivery/${this.currentOrderId}`, {
                    method: 'POST'
                });
            } catch (error) {
                console.error('模拟配送失败:', error);
            }
        },

        // 模拟送达
        async simulateArrival() {
            if (!this.currentOrderId) return;

            try {
                await fetch(`/api/food_orders/simulate_arrival/${this.currentOrderId}`, {
                    method: 'POST'
                });
            } catch (error) {
                console.error('模拟送达失败:', error);
            }
        },

        // 格式化时间
        formatTime(date) {
            return date.toLocaleTimeString('zh-CN', {
                hour: '2-digit',
                minute: '2-digit'
            });
        },

        // 显示订单状态模态窗口
        showOrderStatusModal(order) {
            try {
                console.log('显示订单状态模态窗口，订单:', order);

                // 设置当前订单ID
                this.currentOrderId = order.order_id;

                // 填充订单基本信息
                const orderNumberElement = document.getElementById('orderNumber');
                if (orderNumberElement) {
                    orderNumberElement.textContent = order.order_id;
                }

                const submitTimeElement = document.getElementById('submitTime');
                if (submitTimeElement) {
                    submitTimeElement.textContent = new Date(order.created_at).toLocaleTimeString('zh-CN', {
                        hour: '2-digit',
                        minute: '2-digit'
                    });
                }

                // 更新订单进度
                this.updateOrderProgress(order);

                // 显示模态窗口
                if (this.orderStatusModalInstance) {
                    this.orderStatusModalInstance.show();

                    // 开始轮询订单状态
                    this.startOrderStatusPolling();
                } else {
                    console.error('orderStatusModalInstance 不存在');
                }
            } catch (error) {
                console.error('显示订单状态模态窗口失败:', error);
            }
        },

        // 显示我的订单
        async showMyOrders() {
            try {
                // 获取用户的订单列表
                await this.loadMyOrders();

                // 显示模态窗口
                if (this.myOrdersModalInstance) {
                    this.myOrdersModalInstance.show();
                }
            } catch (error) {
                console.error('显示我的订单失败:', error);
                alert('获取订单列表失败，请稍后重试');
            }
        },

        // 加载我的订单
        async loadMyOrders() {
            try {
                // 获取当前登录用户ID
                let currentUserId = 'elderly_001'; // 默认值

                try {
                    const userResponse = await fetch('/api/auth/current_user');
                    if (userResponse.ok) {
                        const userData = await userResponse.json();
                        if (userData.logged_in && userData.user_type === 'elderly') {
                            currentUserId = userData.user_id;
                        }
                    }
                } catch (error) {
                    console.warn('获取当前用户信息失败，使用默认用户ID:', error);
                    // 尝试从URL参数获取
                    const urlParams = new URLSearchParams(window.location.search);
                    const urlUserId = urlParams.get('user_id');
                    if (urlUserId) {
                        currentUserId = urlUserId;
                    }
                }

                console.log('加载用户订单，用户ID:', currentUserId);

                const response = await fetch(`/api/food_orders/my_orders/${currentUserId}`);
                const result = await response.json();

                if (response.ok && result.success) {
                    this.myOrders = result.orders || [];
                    this.updateOrdersDisplay();
                } else {
                    throw new Error(result.error || '获取订单失败');
                }
            } catch (error) {
                console.error('加载我的订单失败:', error);
                this.myOrders = [];
                this.updateOrdersDisplay();
            }
        },

        // 更新订单显示
        updateOrdersDisplay() {
            const container = document.getElementById('orderListContainer');
            const emptyState = document.getElementById('emptyOrderState');

            if (this.myOrders.length === 0) {
                container.innerHTML = '';
                emptyState.style.display = 'block';
                this.updateOrderStats(0, 0, 0, 0);
                return;
            }

            emptyState.style.display = 'none';

            // 统计各状态订单数量
            let pending = 0, processing = 0, completed = 0;

            // 生成订单列表HTML
            let html = '';
            this.myOrders.forEach(order => {
                // 统计状态
                if (order.status === 'pending_assignment') pending++;
                else if (['assigned', 'delivering', 'delivered'].includes(order.status)) processing++;
                else if (order.status === 'completed') completed++;

                // 获取状态显示
                const statusInfo = this.getOrderStatusInfo(order.status);

                html += `
                    <div class="card mb-3 order-card" onclick="openOrderStatus('${order.order_id}')">
                        <div class="card-body">
                            <div class="row align-items-center">
                                <div class="col-md-2">
                                    <h6 class="mb-1">订单号</h6>
                                    <p class="mb-0 text-primary fw-bold">${order.order_id}</p>
                                </div>
                                <div class="col-md-3">
                                    <h6 class="mb-1">订单时间</h6>
                                    <p class="mb-0">${this.formatDateTime(order.created_at)}</p>
                                </div>
                                <div class="col-md-2">
                                    <h6 class="mb-1">订单金额</h6>
                                    <p class="mb-0 fw-bold">¥${order.final_price}</p>
                                </div>
                                <div class="col-md-2">
                                    <h6 class="mb-1">订单状态</h6>
                                    <span class="badge ${statusInfo.class} fs-6">${statusInfo.text}</span>
                                </div>
                                <div class="col-md-3">
                                    <h6 class="mb-1">菜品</h6>
                                    <p class="mb-0">${order.items.map(item => `${item.name} x${item.quantity}`).join(', ')}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            });

            container.innerHTML = html;
            this.updateOrderStats(this.myOrders.length, pending, processing, completed);
        },

        // 获取订单状态信息
        getOrderStatusInfo(status) {
            switch(status) {
                case 'pending_assignment':
                    return { text: '待分配', class: 'bg-warning' };
                case 'assigned':
                    return { text: '已分配', class: 'bg-info' };
                case 'delivering':
                    return { text: '配送中', class: 'bg-primary' };
                case 'delivered':
                    return { text: '已送达', class: 'bg-success' };
                case 'completed':
                    return { text: '已完成', class: 'bg-secondary' };
                default:
                    return { text: '未知', class: 'bg-light' };
            }
        },

        // 更新订单统计
        updateOrderStats(total, pending, processing, completed) {
            document.getElementById('totalOrdersCount').textContent = total;
            document.getElementById('pendingOrdersCount').textContent = pending;
            document.getElementById('processingOrdersCount').textContent = processing;
            document.getElementById('completedOrdersCount').textContent = completed;
        },

        // 格式化日期时间
        formatDateTime(dateTimeStr) {
            if (!dateTimeStr) return '未知';
            try {
                const date = new Date(dateTimeStr);
                return date.toLocaleString('zh-CN', {
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit'
                });
            } catch (error) {
                return dateTimeStr;
            }
        }
    },
    initFoodOrderingFeatures(vueInstance) {
        // Modal ainstantiation for food ordering was previously done within the changeTab method.
        // We will ensure they are initialized if the services tab is active,
        // or pre-initialize them here if they are always needed.
        // For now, let's assume they are always needed for simplicity,
        // consistent with emergencyModalInstance.
        const foodDetailModalEl = document.getElementById('foodDetailModal');
        if (foodDetailModalEl) {
            vueInstance.foodDetailModalInstance = new bootstrap.Modal(foodDetailModalEl);
        } else {
            // console.error is not strictly necessary here as changeTab will also check.
        }

        const paymentModalEl = document.getElementById('paymentModal');
        if (paymentModalEl) {
            vueInstance.paymentModalInstance = new bootstrap.Modal(paymentModalEl);
        } else {
            // console.error is not strictly necessary here as changeTab will also check.
        }

        const orderStatusModalEl = document.getElementById('orderStatusModal');
        if (orderStatusModalEl) {
            vueInstance.orderStatusModalInstance = new bootstrap.Modal(orderStatusModalEl);
        }

        const myOrdersModalEl = document.getElementById('myOrdersModal');
        if (myOrdersModalEl) {
            vueInstance.myOrdersModalInstance = new bootstrap.Modal(myOrdersModalEl);
        }

        // 设置全局变量以便其他脚本访问
        window.foodOrderManager = vueInstance;
        window.foodOrderModule = this;

        // 初始化筛选功能
        vueInstance.applyFilters();

        // 延迟初始化菜品显示，确保DOM已加载
        setTimeout(() => {
            vueInstance.updateFoodDisplay();
        }, 100);

        console.log('foodOrderManager 已设置为全局变量');
    }
};