new Vue({
    el: '#monitor-app',
    delimiters: ['[[', ']]'],
    data: {
        currentMenu: '实时体征看板',
        currentUser: null,
        currentElderly: null,
        boundElderlyList: [],
        // 图表对象
        charts: {
            trendChart: null,
            activityRadar: null,
            contactGraph: null
        },
        // 地图对象
        map: null,
        // 当前位置信息
        currentLocationInfo: {
            name: '加载中...',
            address: '获取地址中...',
            lastUpdate: '',
            coordinates: ''
        },
        // 当前选中的老人索引
        selectedElderlyIndex: 0,
        // 紧急事件相关数据
        emergencyEvents: [],
        selectedEvent: null,
        responseMessage: '',
        alertEvent: null,
        responseModalInstance: null,
        emergencyAlertModalInstance: null,
        shownAlerts: [] // 记录已显示过弹窗的事件ID
    },
    mounted() {
        // 获取当前用户信息和绑定的老人列表
        this.getCurrentUser();

        // 初始化模态框
        this.initModals();

        // 初始化紧急事件监控
        this.initEmergencyMonitoring();

        // 添加watch监听当前菜单变化，以便在菜单切换时初始化对应的图表
        this.$watch('currentMenu', (newMenu, oldMenu) => {
            // 在组件更新后初始化相应的图表
            this.$nextTick(() => {
                switch(newMenu) {
                    case '历史趋势分析':
                        this.initTrendChart();
                        break;
                    case '活动行为分析':
                        this.initActivityCharts();
                        break;
                    case '实时定位监控':
                        this.initMap();
                        break;
                    case '应急响应中心':
                        this.loadEmergencyEvents();
                        break;
                }
            });
        });
    },
    methods: {
        selectMenu(menu) {
            this.currentMenu = menu;
        },
        getCurrentUser() {
            axios.get('/api/auth/current_user')
                .then(response => {
                    if (response.data.logged_in) {
                        this.currentUser = {
                            id: response.data.user_id,
                            name: response.data.name,
                            type: response.data.user_type
                        };
                        this.getBoundElderlyList();
                    }
                })
                .catch(error => {
                    console.error('获取用户信息失败:', error);
                });
        },
        getBoundElderlyList() {
            axios.get(`/api/family/elderly/${this.currentUser.id}`)
                .then(response => {
                    if (response.data.elderly_list) {
                        this.boundElderlyList = response.data.elderly_list;
                        // 设置当前选中的老人为主绑定老人或第一个老人
                        const primaryElderly = this.boundElderlyList.find(e => e.is_primary);
                        if (primaryElderly) {
                            this.currentElderly = primaryElderly;
                            this.selectedElderlyIndex = this.boundElderlyList.findIndex(e => e.is_primary);
                        } else if (this.boundElderlyList.length > 0) {
                            this.currentElderly = this.boundElderlyList[0];
                            this.selectedElderlyIndex = 0;
                        }

                        if (this.currentElderly) {
                            this.loadElderlyLocationInfo(this.currentElderly.user_id);
                        }
                    }
                })
                .catch(error => {
                    console.error('获取绑定老人列表失败:', error);
                });
        },
        loadElderlyData(elderlyId) {
            // 加载该老人的数据，更新页面内容
            console.log(`加载老人ID ${elderlyId} 的数据`);

            // 根据当前选项卡初始化相应的图表
            this.$nextTick(() => {
                switch(this.currentMenu) {
                    case '历史趋势分析':
                        this.initTrendChart();
                        break;
                    case '活动行为分析':
                        this.initActivityCharts();
                        break;
                    case '实时定位监控':
                        this.initMap();
                        break;
                }
            });
        },

        // 初始化历史趋势图表
        initTrendChart() {
            if (document.getElementById('trendChart')) {
                // 清除旧图表
                if (this.charts.trendChart) {
                    this.charts.trendChart.destroy();
                }

                const ctx = document.getElementById('trendChart').getContext('2d');
                this.charts.trendChart = new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
                        datasets: [
                            {
                                label: '收缩压',
                                data: [145, 142, 138, 146, 150, 142, 140],
                                borderColor: '#1765d5',
                                backgroundColor: 'rgba(23, 101, 213, 0.1)',
                                borderWidth: 2,
                                fill: true,
                                tension: 0.3
                            },
                            {
                                label: '舒张压',
                                data: [92, 88, 85, 90, 93, 87, 85],
                                borderColor: '#5B8FF9',
                                backgroundColor: 'rgba(91, 143, 249, 0.1)',
                                borderWidth: 2,
                                fill: true,
                                tension: 0.3
                            },
                            {
                                label: '空腹血糖',
                                data: [7.2, 7.4, 7.1, 7.6, 7.8, 7.5, 7.3],
                                borderColor: '#ff7875',
                                backgroundColor: 'rgba(255, 120, 117, 0.1)',
                                borderWidth: 2,
                                fill: true,
                                tension: 0.3,
                                yAxisID: 'y1'
                            }
                        ]
                    },
                    options: {
                        responsive: true,
                        scales: {
                            y: {
                                title: {
                                    display: true,
                                    text: '血压 (mmHg)'
                                }
                            },
                            y1: {
                                position: 'right',
                                title: {
                                    display: true,
                                    text: '血糖 (mmol/L)'
                                },
                                grid: {
                                    drawOnChartArea: false
                                }
                            }
                        },
                        plugins: {
                            title: {
                                display: true,
                                text: '近7天健康指标趋势',
                                font: {
                                    size: 16
                                }
                            },
                            tooltip: {
                                mode: 'index',
                                intersect: false
                            },
                            legend: {
                                position: 'bottom'
                            }
                        }
                    }
                });
            }
        },

        // 初始化活动行为分析图表
        initActivityCharts() {
            // 初始化活动雷达图
            if (document.getElementById('activityRadar')) {
                if (this.charts.activityRadar) {
                    this.charts.activityRadar.destroy();
                }

                const radarCtx = document.getElementById('activityRadar').getContext('2d');
                this.charts.activityRadar = new Chart(radarCtx, {
                    type: 'radar',
                    data: {
                        labels: ['家人聚会', '社区活动', '健康锻炼', '医疗就诊', '生活自理', '朋友交往'],
                        datasets: [{
                            label: '参与度评分',
                            data: [85, 40, 55, 75, 90, 45],
                            backgroundColor: 'rgba(23, 101, 213, 0.2)',
                            borderColor: '#1765d5',
                            pointBackgroundColor: '#1765d5',
                            pointBorderColor: '#fff',
                            pointHoverBackgroundColor: '#fff',
                            pointHoverBorderColor: '#1765d5'
                        }]
                    },
                    options: {
                        responsive: true,
                        plugins: {
                            title: {
                                display: true,
                                text: '老人活动参与度雷达图',
                                font: {
                                    size: 16
                                }
                            }
                        },
                        scales: {
                            r: {
                                angleLines: {
                                    display: true
                                },
                                min: 0,
                                max: 100
                            }
                        }
                    }
                });
            }

            // 初始化社交关系图
            if (document.getElementById('contactGraph')) {
                if (this.charts.contactGraph) {
                    this.charts.contactGraph.destroy();
                }

                const graphCtx = document.getElementById('contactGraph').getContext('2d');
                this.charts.contactGraph = new Chart(graphCtx, {
                    type: 'polarArea',
                    data: {
                        labels: ['家庭成员', '邻居', '社区医生', '朋友', '社区工作者'],
                        datasets: [{
                            label: '互动频率',
                            data: [80, 45, 25, 35, 60],
                            backgroundColor: [
                                'rgba(23, 101, 213, 0.7)',
                                'rgba(91, 143, 249, 0.7)',
                                'rgba(96, 183, 96, 0.7)',
                                'rgba(250, 173, 20, 0.7)',
                                'rgba(255, 120, 117, 0.7)'
                            ]
                        }]
                    },
                    options: {
                        responsive: true,
                        plugins: {
                            title: {
                                display: true,
                                text: '老人社交互动频率分析',
                                font: {
                                    size: 16
                                }
                            },
                            legend: {
                                position: 'bottom'
                            }
                        }
                    }
                });
            }
        },

        // 初始化地图
        async initMap() {
            // 确保地图容器存在
            if (document.getElementById('amapRealtime')) {
                // 创建地图实例
                if (!this.map) {
                    this.map = new AMap.Map('amapRealtime', {
                        zoom: 14,
                        center: [120.1626, 30.2729]  // 默认杭州位置
                    });

                    // 添加工具条控件
                    this.map.plugin(["AMap.ToolBar"], function() {
                        const toolBar = new AMap.ToolBar();
                        this.map.addControl(toolBar);
                    });
                }

                // 加载老人的实时位置
                await this.loadElderlyLocation();
            }
        },

        async loadElderlyLocation() {
            try {
                // 使用当前选中的老人
                if (!this.currentElderly) {
                    console.warn('未选择老人');
                    this.showDefaultLocation();
                    return;
                }

                const response = await axios.get(`/api/family/elderly/${this.currentUser.id}/location/${this.currentElderly.user_id}`);

                if (response.data && response.data.coordinates) {
                    this.updateMapWithElderlyLocation(response.data);
                } else {
                    console.warn('未获取到老人位置信息');
                    this.showDefaultLocation();
                }
            } catch (error) {
                console.error('加载老人位置失败:', error);
                this.showDefaultLocation();
            }
        },

        showDefaultLocation() {
            // 显示默认位置
            const defaultLng = 120.1626;
            const defaultLat = 30.2729;

            this.map.setCenter([defaultLng, defaultLat]);

            const marker = new AMap.Marker({
                position: [defaultLng, defaultLat],
                title: '默认位置',
                icon: 'https://webapi.amap.com/theme/v1.3/markers/n/mark_r.png'
            });

            this.map.add(marker);

            // 更新位置信息显示
            this.currentLocationInfo = {
                name: '位置信息获取中...',
                address: '杭州市西湖区（默认位置）',
                lastUpdate: new Date().toLocaleString(),
                coordinates: `${defaultLat}, ${defaultLng}`
            };
        },

        getAddressFromCoordinates(lng, lat, elderlyName) {
            // 使用高德地图API进行逆地理编码
            if (typeof AMap !== 'undefined') {
                AMap.plugin('AMap.Geocoder', () => {
                    const geocoder = new AMap.Geocoder();
                    geocoder.getAddress([lng, lat], (status, result) => {
                        if (status === 'complete' && result.info === 'OK') {
                            const address = result.regeocode.formattedAddress;

                            // 更新位置信息
                            this.currentLocationInfo = {
                                name: elderlyName,
                                address: address,
                                lastUpdate: new Date().toLocaleString(),
                                coordinates: `${lat}, ${lng}`
                            };

                            // 添加信息窗口
                            const infoWindow = new AMap.InfoWindow({
                                content: `
                                    <div style="padding: 10px;">
                                        <h6>${elderlyName}的位置</h6>
                                        <p><strong>地址:</strong> ${address}</p>
                                        <p><strong>坐标:</strong> ${lat}, ${lng}</p>
                                        <p><strong>更新时间:</strong> ${new Date().toLocaleString()}</p>
                                    </div>
                                `,
                                offset: new AMap.Pixel(0, -30)
                            });

                            // 点击标记显示信息窗口
                            const markers = this.map.getAllOverlays('marker');
                            if (markers.length > 0) {
                                markers[0].on('click', () => {
                                    infoWindow.open(this.map, markers[0].getPosition());
                                });
                            }
                        } else {
                            console.error('地址解析失败');
                            this.currentLocationInfo = {
                                name: elderlyName,
                                address: '地址解析失败',
                                lastUpdate: new Date().toLocaleString(),
                                coordinates: `${lat}, ${lng}`
                            };
                        }
                    });
                });
            }
        },

        updateLocationInfo(locationData) {
            // 更新位置信息显示
            this.currentLocationInfo = {
                name: locationData.name,
                address: '正在获取地址...',
                lastUpdate: locationData.last_sync_time || new Date().toLocaleString(),
                coordinates: locationData.gps_location
            };
        },

        // 切换老人
        switchElderly() {
            if (this.boundElderlyList.length > 0) {
                this.currentElderly = this.boundElderlyList[this.selectedElderlyIndex];
                this.loadElderlyLocationInfo(this.currentElderly.user_id);

                // 如果当前在位置监控页面，更新地图
                if (this.currentMenu === '实时定位监控') {
                    this.initMap();
                }
            }
        },

        // 加载老人位置信息
        loadElderlyLocationInfo(elderlyId) {
            if (!this.currentUser || !elderlyId) return;

            axios.get(`/api/family/elderly/${this.currentUser.id}/location/${elderlyId}`)
                .then(response => {
                    const data = response.data;
                    this.currentLocationInfo = {
                        name: data.name,
                        address: data.address,
                        lastUpdate: data.last_sync_time || '位置信息暂未更新',
                        coordinates: data.coordinates || '暂无坐标信息'
                    };

                    // 如果当前在位置监控页面，更新地图
                    if (this.currentMenu === '实时定位监控' && this.map) {
                        this.updateMapWithElderlyLocation(data);
                    }
                })
                .catch(error => {
                    console.error('获取老人位置信息失败:', error);
                    this.currentLocationInfo = {
                        name: this.currentElderly ? this.currentElderly.name : '未知',
                        address: '位置信息获取失败',
                        lastUpdate: '获取失败',
                        coordinates: '暂无坐标信息'
                    };
                });
        },

        // 更新地图显示老人位置
        updateMapWithElderlyLocation(locationData) {
            if (!this.map) return;

            let lng, lat;

            // 解析坐标
            if (locationData.coordinates && locationData.coordinates.includes(',')) {
                [lng, lat] = locationData.coordinates.split(',').map(Number);
            } else {
                // 使用默认坐标（杭州市中心）
                lng = 120.1626;
                lat = 30.2729;
            }

            // 清除现有标记
            this.map.clearMap();

            // 更新地图中心
            this.map.setCenter([lng, lat]);

            // 添加老人位置标记
            const marker = new AMap.Marker({
                position: [lng, lat],
                title: `${locationData.name}的当前位置`,
                icon: 'https://webapi.amap.com/theme/v1.3/markers/n/mark_r.png'
            });

            this.map.add(marker);

            // 添加安全围栏
            const circle = new AMap.Circle({
                center: [lng, lat],
                radius: 500,  // 500米安全围栏
                fillColor: 'rgba(23, 101, 213, 0.1)',
                strokeColor: '#1765d5',
                strokeWeight: 2
            });

            this.map.add(circle);

            // 添加信息窗口
            const infoWindow = new AMap.InfoWindow({
                content: `
                    <div style="padding: 10px;">
                        <h6>${locationData.name}的位置</h6>
                        <p><strong>地址:</strong> ${locationData.address}</p>
                        <p><strong>坐标:</strong> ${lat}, ${lng}</p>
                        <p><strong>更新时间:</strong> ${locationData.last_sync_time || '暂未更新'}</p>
                    </div>
                `,
                offset: new AMap.Pixel(0, -30)
            });

            // 点击标记显示信息窗口
            marker.on('click', () => {
                infoWindow.open(this.map, marker.getPosition());
            });
        },

        refreshLocation() {
            // 刷新位置信息
            if (this.currentElderly) {
                this.loadElderlyLocationInfo(this.currentElderly.user_id);
            }
        },

        // 初始化模态框
        initModals() {
            this.$nextTick(() => {
                const responseModalEl = document.getElementById('responseModal');
                const emergencyAlertModalEl = document.getElementById('emergencyAlertModal');

                if (responseModalEl) {
                    this.responseModalInstance = new bootstrap.Modal(responseModalEl);
                }

                if (emergencyAlertModalEl) {
                    this.emergencyAlertModalInstance = new bootstrap.Modal(emergencyAlertModalEl);
                }
            });
        },

        // 初始化紧急事件监控
        initEmergencyMonitoring() {
            // 定期检查新的紧急事件
            setInterval(() => {
                if (this.currentUser) {
                    this.checkForNewEmergencyEvents();
                }
            }, 3000); // 每3秒检查一次，更快响应
        },

        // 检查新的紧急事件
        async checkForNewEmergencyEvents() {
            try {
                console.log('🔍 检查新的紧急事件...');
                console.log('📋 当前用户:', this.currentUser);

                const response = await axios.get('/api/emergency_event/active', {
                    params: {
                        user_type: 'family',
                        user_id: this.currentUser.id
                    }
                });

                console.log('📡 API响应:', response.data);

                if (response.data.events) {
                    console.log('📋 所有事件:', response.data.events);

                    // 更新事件列表（实时更新）
                    this.emergencyEvents = response.data.events;

                    // 查找待处理且未响应的事件
                    const pendingEvents = response.data.events.filter(event =>
                        event.status === '待处理' &&
                        !event.family_responded &&
                        !event.worker_responded
                    );

                    console.log('🆕 待处理事件:', pendingEvents);

                    // 检查是否有新的待处理事件（不在已显示的弹窗记录中）
                    const newEvents = pendingEvents.filter(event =>
                        !this.shownAlerts.includes(event.event_id)
                    );

                    console.log('🔍 已显示弹窗的事件ID:', this.shownAlerts);
                    console.log('🆕 新事件（未弹窗）:', newEvents);

                    if (newEvents.length > 0) {
                        // 为每个新事件都显示弹窗（按时间顺序，最新的先显示）
                        const latestEvent = newEvents[0]; // 取最新的事件

                        console.log('🚨 发现新的紧急事件，显示弹窗:', latestEvent.event_id);

                        // 记录已显示的弹窗
                        this.shownAlerts.push(latestEvent.event_id);

                        // 丰富事件数据
                        const enrichedEvent = this.enrichEmergencyEventData(latestEvent);

                        // 显示弹窗
                        this.alertEvent = enrichedEvent;
                        if (this.emergencyAlertModalInstance) {
                            this.emergencyAlertModalInstance.show();

                            // 在弹窗显示后初始化地图
                            this.$nextTick(() => {
                                this.initEmergencyAlertMap();
                            });
                        }

                        // 播放提示音
                        this.playAlertSound();
                    } else {
                        console.log('✅ 没有新的紧急事件需要弹窗');
                    }
                } else {
                    console.log('❌ API响应中没有events字段');
                }
            } catch (error) {
                console.error('❌ 检查紧急事件失败:', error);
            }
        },

        // 播放提示音
        playAlertSound() {
            try {
                const audio = new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT');
                audio.play().catch(e => console.log('无法播放提示音:', e));
            } catch (error) {
                console.log('播放提示音失败:', error);
            }
        },

        // 加载紧急事件列表
        async loadEmergencyEvents() {
            if (!this.currentUser) {
                console.log('❌ 用户未登录，无法加载紧急事件');
                return;
            }

            try {
                console.log('📥 加载紧急事件列表...');
                console.log('📋 当前用户:', this.currentUser);

                const response = await axios.get('/api/emergency_event/active', {
                    params: {
                        user_type: 'family',
                        user_id: this.currentUser.id
                    }
                });

                console.log('📡 加载事件API响应:', response.data);

                if (response.data.events) {
                    this.emergencyEvents = response.data.events;
                    console.log('✅ 成功加载紧急事件:', this.emergencyEvents.length, '个');

                    // 清理已显示弹窗记录：只保留当前仍然是"待处理"且未响应的事件ID
                    const currentPendingEventIds = response.data.events
                        .filter(event =>
                            event.status === '待处理' &&
                            !event.family_responded &&
                            !event.worker_responded
                        )
                        .map(event => event.event_id);

                    // 过滤shownAlerts，只保留仍然待处理的事件
                    this.shownAlerts = this.shownAlerts.filter(eventId =>
                        currentPendingEventIds.includes(eventId)
                    );

                    console.log('🧹 清理后的已显示弹窗记录:', this.shownAlerts);
                } else {
                    console.log('❌ API响应中没有events字段');
                }
            } catch (error) {
                console.error('❌ 加载紧急事件失败:', error);
                this.$message?.error('加载紧急事件失败');
            }
        },

        // 刷新紧急事件
        refreshEmergencyEvents() {
            this.loadEmergencyEvents();
        },

        // 测试紧急弹窗
        testEmergencyPopup() {
            const testEvent = {
                event_id: 'TEST001',
                elderly_name: '测试老人',
                emergency_type: 'medical',
                created_at: new Date().toLocaleString(),
                address: '杭州市西湖区西溪路525号',
                location: '30.2741,120.1551', // 杭州坐标
                family_responded: false,
                worker_responded: false
            };

            // 丰富测试事件数据
            this.alertEvent = this.enrichEmergencyEventData(testEvent);

            if (this.emergencyAlertModalInstance) {
                this.emergencyAlertModalInstance.show();

                // 在弹窗显示后初始化地图
                this.$nextTick(() => {
                    this.initEmergencyAlertMap();
                });
            }
        },

        // 获取紧急类型文本
        getEmergencyTypeText(type) {
            const typeMap = {
                'medical': '医疗紧急',
                'fall': '跌倒求助',
                'help': '一般求助'
            };
            return typeMap[type] || type;
        },

        // 获取状态徽章样式
        getStatusBadgeClass(status) {
            const statusMap = {
                '待处理': 'bg-warning',
                '已处理': 'bg-success',
                '部分响应': 'bg-info'
            };
            return statusMap[status] || 'bg-secondary';
        },

        // 响应紧急事件
        async respondToEvent(eventId, message) {
            try {
                const response = await axios.post('/api/emergency_event/respond', {
                    event_id: eventId,
                    user_type: 'family',
                    user_id: this.currentUser.id,
                    response_note: message
                });

                if (response.data.success) {
                    this.showToast('响应成功！您的回复已发送给老人', 'success');
                    this.loadEmergencyEvents(); // 刷新列表

                    // 更新当前弹窗事件的响应状态
                    if (this.alertEvent && this.alertEvent.event_id === eventId) {
                        this.alertEvent.family_responded = true;
                    }
                } else {
                    this.showToast('响应失败：' + response.data.error, 'error');
                }
            } catch (error) {
                console.error('响应失败:', error);
                this.showToast('响应失败，请重试', 'error');
            }
        },

        // 显示响应模态框
        showResponseModal(event) {
            this.selectedEvent = event;
            this.responseMessage = '';
            if (this.responseModalInstance) {
                this.responseModalInstance.show();
            }
        },

        // 提交自定义响应
        async submitResponse() {
            if (!this.responseMessage.trim()) {
                alert('请输入回复消息');
                return;
            }

            await this.respondToEvent(this.selectedEvent.event_id, this.responseMessage);

            // 响应后从已显示弹窗记录中移除（可选）
            // const index = this.shownAlerts.indexOf(this.selectedEvent.event_id);
            // if (index > -1) {
            //     this.shownAlerts.splice(index, 1);
            // }

            if (this.responseModalInstance) {
                this.responseModalInstance.hide();
            }
        },

        // 快速响应
        async quickRespond() {
            if (this.alertEvent) {
                await this.respondToEvent(this.alertEvent.event_id, '我已收到紧急呼叫，正在处理中，请保持冷静。');

                // 响应后从已显示弹窗记录中移除（可选，如果希望已处理的事件不再弹窗）
                // const index = this.shownAlerts.indexOf(this.alertEvent.event_id);
                // if (index > -1) {
                //     this.shownAlerts.splice(index, 1);
                // }

                if (this.emergencyAlertModalInstance) {
                    this.emergencyAlertModalInstance.hide();
                }
            }
        },

        // 显示详细回复模态框
        showDetailedResponse() {
            this.selectedEvent = this.alertEvent;
            this.responseMessage = '';
            if (this.responseModalInstance) {
                this.responseModalInstance.show();
            }
        },

        // 计算事件持续时间
        getElapsedTime(createdAt) {
            try {
                const eventTime = new Date(createdAt);
                const now = new Date();
                const diffMs = now - eventTime;
                const diffMins = Math.floor(diffMs / 60000);
                const diffHours = Math.floor(diffMins / 60);

                if (diffHours > 0) {
                    return `${diffHours}小时${diffMins % 60}分钟`;
                } else {
                    return `${diffMins}分钟`;
                }
            } catch (error) {
                return '计算中...';
            }
        },

        // 初始化紧急事件弹窗地图
        initEmergencyAlertMap() {
            if (!this.alertEvent || !this.alertEvent.location) {
                console.log('没有位置信息，无法初始化地图');
                return;
            }

            try {
                const mapContainer = document.getElementById('emergencyAlertMap');
                if (!mapContainer) {
                    console.log('地图容器不存在');
                    return;
                }

                // 解析GPS坐标
                const [lat, lng] = this.alertEvent.location.split(',').map(Number);

                // 清除现有地图
                if (this.emergencyAlertMap) {
                    this.emergencyAlertMap.destroy();
                }

                // 创建地图实例
                this.emergencyAlertMap = new AMap.Map('emergencyAlertMap', {
                    zoom: 16,
                    center: [lng, lat],
                    mapStyle: 'amap://styles/normal'
                });

                // 添加老人位置标记
                const elderlyMarker = new AMap.Marker({
                    position: [lng, lat],
                    title: `${this.alertEvent.elderly_name}的紧急位置`,
                    icon: new AMap.Icon({
                        size: new AMap.Size(40, 50),
                        image: 'https://webapi.amap.com/theme/v1.3/markers/n/mark_r.png',
                        imageOffset: new AMap.Pixel(0, 0)
                    })
                });

                this.emergencyAlertMap.add(elderlyMarker);

                // 添加紧急区域圆圈
                const emergencyCircle = new AMap.Circle({
                    center: [lng, lat],
                    radius: 100,
                    fillColor: 'rgba(255, 77, 79, 0.2)',
                    strokeColor: '#FF4D4F',
                    strokeWeight: 3,
                    strokeStyle: 'dashed'
                });

                this.emergencyAlertMap.add(emergencyCircle);

                // 添加脉冲动画效果
                const pulseCircle = new AMap.Circle({
                    center: [lng, lat],
                    radius: 50,
                    fillColor: 'rgba(255, 77, 79, 0.4)',
                    strokeColor: '#FF4D4F',
                    strokeWeight: 2
                });

                this.emergencyAlertMap.add(pulseCircle);

                // 查询附近的医院、派出所等紧急资源
                this.searchNearbyEmergencyResources(lng, lat);

                // 计算距离（模拟家属位置）
                this.calculateDistanceToFamily(lng, lat);

            } catch (error) {
                console.error('初始化紧急事件地图失败:', error);
            }
        },

        // 搜索附近的紧急资源
        searchNearbyEmergencyResources(lng, lat) {
            if (typeof AMap === 'undefined') return;

            // 直接设置浙江省行政中心二号院作为附近资源
            const adminCenter = {
                name: '浙江省行政中心二号院',
                location: {
                    lng: lng + 0.01, // 模拟位置偏移
                    lat: lat + 0.005
                }
            };

            // 添加行政中心标记
            const centerMarker = new AMap.Marker({
                position: [adminCenter.location.lng, adminCenter.location.lat],
                title: adminCenter.name,
                icon: new AMap.Icon({
                    size: new AMap.Size(25, 34),
                    image: 'https://webapi.amap.com/theme/v1.3/markers/n/mark_b.png'
                })
            });

            this.emergencyAlertMap.add(centerMarker);

            // 更新附近资源信息
            this.$set(this.alertEvent, 'nearbyResources', `最近资源: ${adminCenter.name}`);
        },

        // 计算到家属的距离（模拟）
        calculateDistanceToFamily(lng, lat) {
            // 模拟家属位置（实际应用中应该获取真实位置）
            const familyLng = lng + 0.01; // 模拟距离
            const familyLat = lat + 0.01;

            // 计算距离（简单的直线距离计算）
            const distance = this.calculateDistance(lat, lng, familyLat, familyLng);
            this.$set(this.alertEvent, 'distanceToFamily', `约${distance.toFixed(1)}公里`);
        },

        // 计算两点间距离
        calculateDistance(lat1, lng1, lat2, lng2) {
            const R = 6371; // 地球半径（公里）
            const dLat = (lat2 - lat1) * Math.PI / 180;
            const dLng = (lng2 - lng1) * Math.PI / 180;
            const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
                      Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
                      Math.sin(dLng/2) * Math.sin(dLng/2);
            const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
            return R * c;
        },

        // 丰富紧急事件数据
        enrichEmergencyEventData(event) {
            // 添加模拟的老人详细信息
            this.$set(event, 'elderly_age', 75);
            this.$set(event, 'elderly_gender', '男');
            this.$set(event, 'elderly_phone', '138****5678');

            // 添加健康风险信息
            this.$set(event, 'healthRisks', '高血压、糖尿病患者，请注意血压和血糖监测');
            this.$set(event, 'bloodPressure', '146/90');
            this.$set(event, 'heartRate', '78');
            this.$set(event, 'bloodSugar', '7.6');

            // 添加紧急联系人
            this.$set(event, 'emergencyContacts', [
                { id: 1, name: '张医生', relation: '家庭医生', phone: '139****1234' },
                { id: 2, name: '李护士', relation: '社区护士', phone: '138****5678' },
                { id: 3, name: '王大爷', relation: '邻居', phone: '137****9012' }
            ]);



            return event;
        }
    }
});