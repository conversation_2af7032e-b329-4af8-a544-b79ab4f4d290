const emergencyCallModule = {
    data: {
        emergencyModalInstance: null,
        emergencyStatus: '',
        currentLocation: null,
        emergencyType: null,
        countdownTimer: 10,
        emergencyMap: null,
        countdownInterval: null,
        pollingInterval: null,
        buzzerAudio: null,
        currentEventId: null,
        responderInfo: '',
        // 新增：详细响应信息
        familyResponse: null,
        workerResponse: null,
        currentEvent: null,
    },
    methods: {
        showEmergencyModal() {
            // Reset status
            this.emergencyType = null;
            this.emergencyStatus = '';

            if (this.emergencyModalInstance) {
                this.emergencyModalInstance.show();
            } else {
                // Fallback if instance somehow wasn't created during mounted
                const modalEl = document.getElementById('emergencyModal');
                if (modalEl) {
                    console.warn("Emergency modal instance was not ready, creating now.");
                    this.emergencyModalInstance = new bootstrap.Modal(modalEl);
                     // Re-attach listener if we are creating it here, though this path should ideally not be hit.
                    modalEl.addEventListener('hidden.bs.modal', () => {
                        this.stopBuzzer();
                        this.stopCountdown();
                        this.emergencyStatus = '';
                    });
                    this.emergencyModalInstance.show();
                } else {
                    console.error("Emergency modal element not found.");
                }
            }
        },
        initEmergencyMap(containerId = 'emergencyMap', showRescue = false) {
            // 这里使用已有的高德地图API
            try {
                const container = document.getElementById(containerId);
                if (!container) return;

                // 清除现有地图
                if (this.emergencyMap) {
                    this.emergencyMap.destroy();
                }

                // 创建地图实例
                const map = new AMap.Map(containerId, {
                    zoom: 15,
                    center: [this.currentLocation?.lng || 116.4074, this.currentLocation?.lat || 39.9042]
                });

                // 添加标记
                const marker = new AMap.Marker({
                    position: [this.currentLocation?.lng || 116.4074, this.currentLocation?.lat || 39.9042],
                    title: '当前位置'
                });

                map.add(marker);

                // 添加定位圆圈
                const circle = new AMap.Circle({
                    center: [this.currentLocation?.lng || 116.4074, this.currentLocation?.lat || 39.9042],
                    radius: 50,
                    fillColor: 'rgba(255,77,79,0.2)',
                    strokeColor: '#FF4D4F',
                    strokeWeight: 2
                });

                map.add(circle);

                // 如果显示救援信息，添加救援路线
                if (showRescue || this.emergencyStatus === 'connected') {
                    // 模拟救援路线
                    const path = [
                        [this.currentLocation?.lng || 116.4074, this.currentLocation?.lat || 39.9042],
                        [(this.currentLocation?.lng || 116.4074) + 0.005, (this.currentLocation?.lat || 39.9042) + 0.002],
                        [(this.currentLocation?.lng || 116.4074) + 0.008, (this.currentLocation?.lat || 39.9042) - 0.001]
                    ];

                    const polyline = new AMap.Polyline({
                        path: path,
                        strokeColor: '#1890FF',
                        strokeWeight: 5,
                        strokeStyle: 'solid'
                    });

                    map.add(polyline);

                    // 救援人员位置标记
                    const rescuerMarker = new AMap.Marker({
                        position: path[path.length - 1],
                        title: '救援人员',
                        content: '<div style="background-color: #1890FF; color: white; padding: 5px 10px; border-radius: 50%; box-shadow: 0 0 10px rgba(0,0,0,0.2);"><i class="bi bi-truck"></i></div>'
                    });

                    map.add(rescuerMarker);
                }

                this.emergencyMap = map;
            } catch (error) {
                console.error('初始化地图失败:', error);
            }
        },
        getAddress(lng, lat) {
            // 使用高德地图API进行逆地理编码
            if (typeof AMap !== 'undefined') {
                AMap.plugin('AMap.Geocoder', () => {
                    const geocoder = new AMap.Geocoder();
                    geocoder.getAddress([lng, lat], (status, result) => {
                        if (status === 'complete' && result.info === 'OK') {
                            const address = result.regeocode.formattedAddress;
                            document.getElementById('current-address').textContent = address;

                            // 保存地址信息用于发送
                            this.currentLocation = {
                                address: address,
                                lng: lng,
                                lat: lat
                            };
                        } else {
                            document.getElementById('current-address').textContent = '地址解析失败';
                        }
                    });
                });
            }
        },
        selectEmergencyType(type) {
            // 设置紧急情况类型
            let statusText = '';
            switch(type) {
                case 'medical':
                    statusText = '医疗紧急情况已报告，正在联系医疗人员...';
                    break;
                case 'fall':
                    statusText = '跌倒求助信息已发送，正在联系附近救援人员...';
                    break;
                case 'help':
                    statusText = '求助信息已发送，社区工作人员将尽快联系您...';
                    break;
            }
            this.emergencyStatus = statusText;
        },
        async startEmergencyCall() {
            if (!this.emergencyType) return;

            // 重置响应信息，确保新的紧急呼叫开始时清空之前的响应
            this.resetResponseInfo();

            // 开始蜂鸣音效
            this.startBuzzer();

            // 更新状态
            this.emergencyStatus = 'calling';

            // 重置倒计时 - 改为等待时间计数器
            this.countdownTimer = 0;

            // 开始计时（向上计数，显示等待时间）
            this.startWaitingTimer();

            // 获取位置
            await this.getLocation();

            // 初始化地图
            this.$nextTick(() => {
                this.initEmergencyMap();
            });

            // 发送紧急事件到后端
            try {
                console.log('🚨 发送紧急呼叫请求...');
                const response = await fetch('/api/emergency_event/create', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        elderly_id: 'E01', // 使用数据库中存在的ID
                        emergency_type: this.emergencyType,
                        location: this.currentLocation ? `${this.currentLocation.lat},${this.currentLocation.lng}` : null,
                        address: this.currentLocation ? this.currentLocation.address : '位置获取失败'
                    })
                });

                const result = await response.json();
                console.log('📡 后端响应:', result);

                if (result.success) {
                    console.log('✅ 紧急事件已创建:', result.event_id);
                    this.currentEventId = result.event_id;

                    // 开始轮询检查响应状态
                    this.startPollingForResponse();
                } else {
                    console.error('❌ 创建紧急事件失败:', result.error);
                    alert('紧急呼叫发送失败，请重试');
                    this.cancelEmergencyCall();
                }
            } catch (error) {
                console.error('❌ 发送紧急事件失败:', error);
                alert('网络错误，请检查网络连接后重试');
                this.cancelEmergencyCall();
            }
        },
        cancelEmergencyCall() {
            this.stopBuzzer();
            this.stopCountdown();

            // 停止轮询
            if (this.pollingInterval) {
                clearInterval(this.pollingInterval);
                this.pollingInterval = null;
            }

            this.emergencyStatus = '';
            this.currentEventId = null;

            // 重置响应信息
            this.resetResponseInfo();

            if (this.emergencyModalInstance) {
                this.emergencyModalInstance.hide();
            }
        },
        startBuzzer() {
            if (this.buzzerAudio) {
                this.buzzerAudio.play().catch(e => console.error('无法播放音频:', e));
            }
        },
        stopBuzzer() {
            if (this.buzzerAudio) {
                this.buzzerAudio.pause();
                this.buzzerAudio.currentTime = 0;
            }
        },
        startWaitingTimer() {
            this.stopCountdown(); // 确保先停止之前的计时器

            this.countdownInterval = setInterval(() => {
                this.countdownTimer++; // 向上计数，显示等待时间
            }, 1000);
        },
        startCountdown() {
            this.stopCountdown(); // 确保先停止之前的倒计时

            this.countdownInterval = setInterval(() => {
                if (this.countdownTimer > 1) {
                    this.countdownTimer--;
                } else {
                    this.stopCountdown();
                }
            }, 1000);
        },
        stopCountdown() {
            if (this.countdownInterval) {
                clearInterval(this.countdownInterval);
                this.countdownInterval = null;
            }
        },
        // 轮询检查是否有人响应
        startPollingForResponse() {
            if (this.pollingInterval) {
                clearInterval(this.pollingInterval);
            }

            this.pollingInterval = setInterval(async () => {
                try {
                    console.log('🔍 检查紧急事件响应状态...');
                    console.log('🔍 当前事件ID:', this.currentEventId);

                    // 直接查询特定事件的状态
                    const response = await fetch(`/api/emergency_event/status/${this.currentEventId}`);
                    const result = await response.json();

                    if (result.success && result.event) {
                        const currentEvent = result.event;
                        console.log('📋 当前事件状态:', currentEvent);

                        // 检查是否有人响应
                        if (currentEvent.family_responded || currentEvent.worker_responded) {
                            console.log('✅ 有人响应了紧急呼叫！');

                            // 如果是第一次有人响应，停止计时器和蜂鸣音，但继续轮询检查其他响应者
                            if (this.emergencyStatus !== 'connected') {
                                this.stopCountdown();
                                this.stopBuzzer();
                                // 更新状态为已连接
                                this.emergencyStatus = 'connected';
                            }

                            // 不要停止轮询！继续检查是否有新的响应者
                            // 只有当所有可能的响应者都响应了，或者用户手动关闭时才停止轮询

                            // 保存当前事件信息
                            this.currentEvent = currentEvent;

                            // 处理家属响应信息 - 确保每次都更新
                            if (currentEvent.family_responded) {
                                this.familyResponse = {
                                    responder_name: currentEvent.family_responder_name || '家属',
                                    response_time: this.formatResponseTime(currentEvent.family_response_time),
                                    message: currentEvent.family_response_note || ''
                                };
                                console.log('✅ 家属响应信息已更新:', this.familyResponse);
                            } else {
                                // 如果家属未响应，清空家属响应信息
                                this.familyResponse = null;
                            }

                            // 处理工作人员响应信息 - 确保每次都更新
                            if (currentEvent.worker_responded) {
                                this.workerResponse = {
                                    responder_name: currentEvent.worker_responder_name || '社区工作人员',
                                    response_time: this.formatResponseTime(currentEvent.worker_response_time),
                                    message: currentEvent.worker_response_note || ''
                                };
                                console.log('✅ 工作人员响应信息已更新:', this.workerResponse);
                            } else {
                                // 如果工作人员未响应，清空工作人员响应信息
                                this.workerResponse = null;
                            }

                            // 显示响应者信息（用于兼容旧版本显示）
                            let responderInfo = '';
                            if (currentEvent.family_responded) {
                                const familyName = currentEvent.family_responder_name || '家属';
                                responderInfo += `${familyName}已响应`;
                            }
                            if (currentEvent.worker_responded) {
                                if (responderInfo) responderInfo += '，';
                                const workerName = currentEvent.worker_responder_name || '社区工作人员';
                                responderInfo += `${workerName}已响应`;
                            }

                            this.responderInfo = responderInfo;

                            // 更新地图显示救援路线
                            this.$nextTick(() => {
                                this.initEmergencyMap('emergencyMap', true);
                            });

                            // 继续轮询，不要return，以便检查其他响应者的状态更新
                        } else {
                            console.log('⏳ 仍在等待响应...');
                        }
                    } else {
                        console.log('❌ 获取事件状态失败:', result.error || '未知错误');
                    }
                } catch (error) {
                    console.error('❌ 检查响应状态失败:', error);
                }
            }, 2000); // 每2秒检查一次，更频繁
        },
        async getLocation() {
            try {
                // 模拟获取位置信息
                // 实际应用中应使用浏览器的定位API
                await new Promise(resolve => setTimeout(resolve, 1500));
                this.currentLocation = {
                    lat: 30.2741,    // Changed to Hangzhou's approximate latitude
                    lng: 120.1551,   // Changed to Hangzhou's approximate longitude
                    address: '杭州市西湖区西溪路' // Address remains Hangzhou
                };
                return this.currentLocation;
            } catch (error) {
                console.error('获取位置失败:', error);
                this.currentLocation = {
                    lat: 30.2741,    // Fallback to Hangzhou's latitude
                    lng: 120.1551,   // Fallback to Hangzhou's longitude
                    address: '杭州市西湖区西溪路' // Default location address
                };
                return this.currentLocation;
            }
        },
        // 格式化响应时间
        formatResponseTime(timeString) {
            if (!timeString) return '刚刚';
            try {
                const responseTime = new Date(timeString);
                const now = new Date();
                const diffMs = now - responseTime;
                const diffMins = Math.floor(diffMs / 60000);

                if (diffMins < 1) {
                    return '刚刚';
                } else if (diffMins < 60) {
                    return `${diffMins}分钟前`;
                } else {
                    return responseTime.toLocaleString('zh-CN', {
                        month: '2-digit',
                        day: '2-digit',
                        hour: '2-digit',
                        minute: '2-digit'
                    });
                }
            } catch (error) {
                return '刚刚';
            }
        },
        // 确认收到响应
        confirmReceived() {
            // 可以在这里添加确认收到的逻辑
            alert('已确认收到响应信息，请保持冷静等待救援人员到达。');
            this.cancelEmergencyCall();
        },
        // 拨打120急救电话
        callEmergencyServices() {
            if (confirm('确定要拨打120急救电话吗？')) {
                // 在实际应用中，这里可以调用系统的拨号功能
                alert('正在为您拨打120急救电话...\n\n请准备好说明您的位置和紧急情况。');
                // 模拟拨号
                window.open('tel:120', '_self');
            }
        },
        // 重置响应信息
        resetResponseInfo() {
            this.familyResponse = null;
            this.workerResponse = null;
            this.currentEvent = null;
            this.responderInfo = '';
            console.log('🔄 响应信息已重置');
        },

        // 获取详细的事件状态信息
        async getDetailedEventStatus(eventId) {
            try {
                const response = await fetch(`/api/emergency_event/status/${eventId}`);
                const result = await response.json();

                if (result.success && result.event) {
                    const currentEvent = result.event;

                    // 保存当前事件信息
                    this.currentEvent = currentEvent;

                    // 处理家属响应信息 - 确保每次都更新
                    if (currentEvent.family_responded) {
                        this.familyResponse = {
                            responder_name: currentEvent.family_responder_name || '家属',
                            response_time: this.formatResponseTime(currentEvent.family_response_time),
                            message: currentEvent.family_response_note || ''
                        };
                        console.log('✅ 详细状态-家属响应信息已更新:', this.familyResponse);
                    } else {
                        this.familyResponse = null;
                    }

                    // 处理工作人员响应信息 - 确保每次都更新
                    if (currentEvent.worker_responded) {
                        this.workerResponse = {
                            responder_name: currentEvent.worker_responder_name || '社区工作人员',
                            response_time: this.formatResponseTime(currentEvent.worker_response_time),
                            message: currentEvent.worker_response_note || ''
                        };
                        console.log('✅ 详细状态-工作人员响应信息已更新:', this.workerResponse);
                    } else {
                        this.workerResponse = null;
                    }

                    // 显示响应者信息（用于兼容旧版本显示）
                    let responderInfo = '';
                    if (currentEvent.family_responded) {
                        const familyName = currentEvent.family_responder_name || '家属';
                        responderInfo += `${familyName}已响应`;
                    }
                    if (currentEvent.worker_responded) {
                        if (responderInfo) responderInfo += '，';
                        const workerName = currentEvent.worker_responder_name || '社区工作人员';
                        responderInfo += `${workerName}已响应`;
                    }

                    this.responderInfo = responderInfo;

                    console.log('✅ 获取详细事件状态成功:', {
                        familyResponse: this.familyResponse,
                        workerResponse: this.workerResponse,
                        responderInfo: this.responderInfo
                    });
                }
            } catch (error) {
                console.error('❌ 获取详细事件状态失败:', error);
            }
        },
    },
    // mounted 钩子中的初始化逻辑将通过一个单独的函数暴露出去
    initEmergencyFeatures(vueInstance) {
        // 初始化紧急呼叫音频
        vueInstance.buzzerAudio = document.getElementById('emergencyAudio');

        // 监听模态窗口事件
        const emergencyModalEl = document.getElementById('emergencyModal');
        if (emergencyModalEl) {
            vueInstance.emergencyModalInstance = new bootstrap.Modal(emergencyModalEl);
            emergencyModalEl.addEventListener('hidden.bs.modal', () => {
                // 模态窗口关闭时停止蜂鸣音效
                vueInstance.stopBuzzer();
                vueInstance.stopCountdown();
                vueInstance.emergencyStatus = '';
            });
        }
    }
};