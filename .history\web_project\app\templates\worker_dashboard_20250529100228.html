{% extends "base.html" %}

{% block title %}社区工作人员 - 居家养老健康管理系统{% endblock %}

{% block head %}
<style>
    .status-indicator {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        display: inline-block;
        margin-right: 5px;
    }

    .status-pending { background-color: #ffc107; }
    .status-responded { background-color: #17a2b8; }
    .status-resolved { background-color: #28a745; }

    .emergency-call {
        border-left: 4px solid #dc3545;
    }

    .elderly-card {
        transition: all 0.3s;
    }

    .elderly-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
    }

    .map-container {
        height: 400px;
        border-radius: 8px;
        overflow: hidden;
    }

    .stats-card {
        border-radius: 8px;
        border-left: 4px solid;
        transition: all 0.2s;
    }

    .stats-card:hover {
        transform: translateY(-3px);
    }

    .stats-pending { border-left-color: #ffc107; }
    .stats-responded { border-left-color: #17a2b8; }
    .stats-resolved { border-left-color: #28a745; }
    .stats-total { border-left-color: #6c757d; }

    .meal-service-card {
        border-left: 4px solid #28a745;
        margin-bottom: 15px;
        transition: all 0.3s;
    }

    .meal-service-card:hover {
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        transform: translateY(-3px);
    }

    .service-status {
        width: 100px;
        text-align: center;
        padding: 5px;
        border-radius: 20px;
        font-size: 0.9rem;
        font-weight: bold;
    }

    .status-pending-meal {
        background-color: rgba(255, 193, 7, 0.2);
        color: #ffc107;
    }

    .status-assigned {
        background-color: rgba(23, 162, 184, 0.2);
        color: #17a2b8;
    }

    .status-delivered {
        background-color: rgba(40, 167, 69, 0.2);
        color: #28a745;
    }

    .status-delivering {
        background-color: rgba(0, 123, 255, 0.2);
        color: #007bff;
    }

    .status-completed {
        background-color: rgba(108, 117, 125, 0.2);
        color: #6c757d;
    }

    .staff-selector {
        max-width: 250px;
    }

    .nav-tabs .nav-link {
        font-size: 1.1rem;
        font-weight: 500;
    }

    .service-item-details {
        background-color: #f8f9fa;
        padding: 15px;
        border-radius: 8px;
        margin-top: 10px;
        border-left: 3px solid #6c757d;
    }

    .meal-items {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        margin-top: 10px;
    }

    .meal-item {
        background-color: #fff;
        padding: 5px 10px;
        border-radius: 20px;
        border: 1px solid #dee2e6;
        font-size: 0.9rem;
    }

    .assignment-form {
        background-color: #f8f9fa;
        padding: 15px;
        border-radius: 8px;
        margin-top: 15px;
        border: 1px solid #dee2e6;
    }

    /* 工作人员端订单进度条样式 */
    .order-progress-worker {
        margin-top: 20px;
        padding: 20px;
        background-color: #f8f9fa;
        border-radius: 10px;
        border: 1px solid #e9ecef;
    }

    .progress-steps-worker {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        position: relative;
        margin-top: 15px;
    }

    .progress-steps-worker::before {
        content: '';
        position: absolute;
        top: 20px;
        left: 0;
        right: 0;
        height: 2px;
        background-color: #dee2e6;
        z-index: 1;
    }

    .step-worker {
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;
        position: relative;
        flex: 1;
        z-index: 2;
    }

    .step-icon-worker {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background-color: #dee2e6;
        color: #6c757d;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 18px;
        margin-bottom: 10px;
        transition: all 0.3s ease;
        border: 3px solid #fff;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .step-content-worker {
        max-width: 120px;
    }

    .step-content-worker h6 {
        font-size: 0.9rem;
        margin-bottom: 5px;
        font-weight: 600;
    }

    .step-content-worker small {
        font-size: 0.75rem;
        line-height: 1.2;
    }

    /* 激活状态 */
    .step-worker.active .step-icon-worker {
        background-color: #007bff;
        color: white;
        animation: pulse 2s infinite;
    }

    .step-worker.active .step-content-worker h6 {
        color: #007bff;
        font-weight: bold;
    }

    /* 完成状态 */
    .step-worker.completed .step-icon-worker {
        background-color: #28a745;
        color: white;
    }

    .step-worker.completed .step-content-worker h6 {
        color: #28a745;
        font-weight: bold;
    }

    /* 等待状态 */
    .step-worker.pending .step-icon-worker {
        background-color: #ffc107;
        color: white;
    }

    .step-worker.pending .step-content-worker h6 {
        color: #ffc107;
        font-weight: bold;
    }

    /* 脉冲动画 */
    @keyframes pulse {
        0% {
            box-shadow: 0 0 0 0 rgba(0, 123, 255, 0.7);
        }
        70% {
            box-shadow: 0 0 0 10px rgba(0, 123, 255, 0);
        }
        100% {
            box-shadow: 0 0 0 0 rgba(0, 123, 255, 0);
        }
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
        .progress-steps-worker {
            flex-direction: column;
            align-items: flex-start;
        }

        .progress-steps-worker::before {
            display: none;
        }

        .step-worker {
            flex-direction: row;
            align-items: center;
            text-align: left;
            margin-bottom: 15px;
            width: 100%;
        }

        .step-icon-worker {
            margin-right: 15px;
            margin-bottom: 0;
        }

        .step-content-worker {
            max-width: none;
        }
    }
</style>
{% endblock %}

{% block content %}
<div id="worker-app" class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card shadow-sm">
                <div class="card-body d-flex align-items-center">
                    <div class="avatar bg-primary text-white rounded-circle p-3 me-3">
                        <h2 class="mb-0">👩‍⚕️</h2>
                    </div>
                    <div>
                        <h4 class="mb-1">欢迎，刘强</h4>
                        <p class="text-muted mb-0">社区工作人员 | 负责区域: A区</p>
                    </div>
                    <div class="ms-auto">
                        <span class="badge bg-success">当前在线</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card stats-card stats-total shadow-sm">
                <div class="card-body">
                    <h6 class="text-muted">总老年人数</h6>
                    <h3 class="mb-0">24</h3>
                    <small class="text-success">+2 本月新增</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stats-card stats-pending shadow-sm">
                <div class="card-body">
                    <h6 class="text-muted">待处理紧急呼叫</h6>
                    <h3 class="mb-0">2</h3>
                    <small class="text-danger">需要立即处理</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stats-card stats-responded shadow-sm">
                <div class="card-body">
                    <h6 class="text-muted">已响应服务</h6>
                    <h3 class="mb-0">8</h3>
                    <small class="text-primary">正在进行中</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stats-card stats-resolved shadow-sm">
                <div class="card-body">
                    <h6 class="text-muted">本周已解决</h6>
                    <h3 class="mb-0">15</h3>
                    <small class="text-success">完成率 93%</small>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-8">
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">服务任务管理中心</h5>
                </div>
                <div class="card-body">
                    <!-- 任务类型选项卡 -->
                    <ul class="nav nav-tabs mb-3" id="serviceTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="emergency-tab" data-bs-toggle="tab" data-bs-target="#emergency-content" type="button" role="tab">
                                <i class="bi bi-exclamation-triangle me-1"></i> 紧急呼叫
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="meal-tab" data-bs-toggle="tab" data-bs-target="#meal-content" type="button" role="tab">
                                <i class="bi bi-cup-hot me-1"></i> 订餐送餐
                            </button>
                        </li>

                    </ul>

                    <!-- 选项卡内容 -->
                    <div class="tab-content" id="serviceTabContent">
                        <!-- 紧急呼叫选项卡内容 -->
                        <div class="tab-pane fade show active" id="emergency-content" role="tabpanel">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h5 class="mb-0">紧急呼叫与服务请求</h5>
                                <div class="d-flex align-items-center">
                                    <button class="btn btn-outline-secondary me-2" @click="loadEmergencyEvents" :disabled="isLoadingEmergencyEvents">
                                        <i class="bi bi-arrow-clockwise"></i>
                                        <span v-if="isLoadingEmergencyEvents">刷新中...</span>
                                        <span v-else>刷新</span>
                                    </button>
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="showResolvedSwitch" v-model="showResolved">
                                        <label class="form-check-label" for="showResolvedSwitch">显示已解决</label>
                                    </div>
                                </div>
                            </div>

                    <!-- 无紧急呼叫状态 -->
                    <div v-if="emergencyCalls.length === 0" class="text-center py-4">
                        <i class="bi bi-shield-check fs-1 text-success"></i>
                        <p class="mt-2 text-muted">暂无紧急呼叫</p>
                    </div>

                    <!-- 紧急呼叫列表 -->
                    <ul v-else class="list-group">
                        <li v-for="call in emergencyCalls" :key="call.id" class="list-group-item emergency-call">
                            <div class="d-flex align-items-center">
                                <div class="me-3">
                                    <span class="badge" :class="call.status === '已响应' ? 'bg-success' : 'bg-danger'">
                                        [[ call.status === '已响应' ? '已响应' : '紧急' ]]
                                    </span>
                                </div>
                                <div>
                                    <h6 class="mb-1">[[ call.name ]] ([[ call.elderly_id ]])</h6>
                                    <p class="mb-0 text-muted small">
                                        <i class="bi bi-geo-alt-fill me-1"></i>位置: [[ call.address ]] |
                                        <i class="bi bi-clock-fill ms-2 me-1"></i>时间: [[ call.time ]]
                                        <span v-if="call.emergency_type" class="ms-2">
                                            <i class="bi bi-exclamation-triangle-fill me-1"></i>类型: [[ getEmergencyTypeText(call.emergency_type) ]]
                                        </span>
                                    </p>
                                </div>
                                <div class="ms-auto">
                                    <button v-if="call.status !== '已响应'"
                                            class="btn btn-sm btn-outline-primary"
                                            @click="respondToCall(call.id)">
                                        响应
                                    </button>
                                    <span v-else class="badge bg-success">已响应</span>
                                </div>
                            </div>
                        </li>
                    </ul>
                        </div>

                        <!-- 订餐送餐选项卡内容 -->
                        <div class="tab-pane fade" id="meal-content" role="tabpanel">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <div>
                                    <h5 class="mb-0">今日订餐送餐服务</h5>
                                    <small class="text-muted">
                                        总计: [[ mealServicesStats.total ]] |
                                        待分配: <span class="text-warning">[[ mealServicesStats.pending ]]</span> |
                                        已分配: <span class="text-primary">[[ mealServicesStats.assigned ]]</span> |
                                        已送达: <span class="text-success">[[ mealServicesStats.delivered ]]</span>
                                    </small>
                                </div>
                            <div class="d-flex align-items-center">
                                    <div class="input-group me-2 staff-selector">
                                        <select class="form-select" v-model="selectedStaff">
                                            <option value="">选择服务人员</option>
                                            <option value="1">王小明</option>
                                            <option value="2">李玲</option>
                                            <option value="3">赵强</option>
                                        </select>
                                        <button class="btn btn-primary" @click="assignAllToStaff">分配</button>
                                    </div>
                                    <button class="btn btn-outline-secondary me-2" @click="loadMealOrders" :disabled="isLoadingMealServices">
                                        <i class="bi bi-arrow-clockwise"></i>
                                        <span v-if="isLoadingMealServices">刷新中...</span>
                                        <span v-else>刷新</span>
                                    </button>
                                    <button class="btn btn-outline-secondary">
                                        <i class="bi bi-funnel"></i> 筛选
                                    </button>
                                </div>
                            </div>

                            <!-- 加载状态 -->
                            <div v-if="isLoadingMealServices" class="text-center py-4">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">加载中...</span>
                                </div>
                                <p class="mt-2 text-muted">正在加载订餐订单...</p>
                            </div>

                            <!-- 无订单状态 -->
                            <div v-else-if="mealServices.length === 0" class="text-center py-4">
                                <i class="bi bi-inbox fs-1 text-muted"></i>
                                <p class="mt-2 text-muted">暂无订餐订单</p>
                            </div>

                            <!-- 订餐服务列表 -->
                            <div v-else v-for="service in mealServices" :key="service.id" class="meal-service-card card mb-3">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div class="d-flex align-items-center">
                                            <div class="me-3">
                                                <span class="avatar text-white rounded-circle d-flex align-items-center justify-content-center"
                                                      :class="getAvatarClass(service.status)"
                                                      style="width: 40px; height: 40px;">
                                                    [[ service.name ? service.name.charAt(0) : 'U' ]]
                                                </span>
                                            </div>
                                            <div>
                                                <h6 class="mb-1">[[ service.name ]] ([[ service.elderly_id ]])</h6>
                                                <p class="mb-0 text-muted small">
                                                    <i class="bi bi-geo-alt-fill me-1"></i>[[ service.address ]]
                                                    <i class="bi bi-clock-fill ms-2 me-1"></i>[[ service.time ]]
                                                </p>
                                            </div>
                                        </div>
                                        <div class="d-flex align-items-center">
                                            <div class="service-status me-3" :class="getStatusClass(service.status)">
                                                [[ getStatusText(service.status) ]]
                                            </div>
                                            <div class="dropdown">
                                                <button class="btn btn-outline-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                                    管理
                                                </button>
                                                <ul class="dropdown-menu" z-index="100">
                                                    <li><a class="dropdown-item" href="#" @click.prevent="showDetails(service.id)">查看详情</a></li>
                                                    <li v-if="service.status === 'pending'">
                                                        <a class="dropdown-item" href="#" @click.prevent="reassignService(service.id)">分配服务人员</a>
                                                    </li>
                                                    <li v-if="service.status === 'assigned'">
                                                        <a class="dropdown-item" href="#" @click.prevent="reassignService(service.id)">重新分配</a>
                                                    </li>
                                                    <li v-if="service.status === 'assigned'">
                                                        <a class="dropdown-item" href="#" @click.prevent="markAsDelivered(service.id)">标记为已送达</a>
                                                    </li>
                                                    <li v-if="service.status === 'delivered'">
                                                        <a class="dropdown-item" href="#" @click.prevent="viewFeedback(service.id)">查看反馈</a>
                                                    </li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 详情区域，初始隐藏，点击后显示 -->
                                    <div class="service-item-details" v-if="activeService === service.id">
                                        <div class="d-flex justify-content-between mb-3">
                                            <h6 class="mb-0"><i class="bi bi-card-list me-2"></i>订单详情</h6>
                                            <span class="text-muted">订单号: [[ service.id ]]</span>
                                        </div>



                                        <div class="row mb-3">
                                            <div class="col-md-6">
                                                <p class="mb-2"><i class="bi bi-telephone-fill me-2 text-primary"></i>联系电话: [[ service.phone || '未设置' ]]</p>
                                                <p class="mb-2"><i class="bi bi-person-fill me-2 text-primary"></i>服务人员: [[ service.staff || '未分配' ]]</p>
                                                <p class="mb-0"><i class="bi bi-chat-left-text-fill me-2 text-primary"></i>备注: [[ service.special_notes || '无特殊备注' ]]</p>
                                            </div>
                                            <div class="col-md-6">
                                                <p class="mb-2"><i class="bi bi-calendar-check-fill me-2 text-primary"></i>订餐时间: [[ formatDateTime(service.created_at) ]]</p>
                                                <p class="mb-2"><i class="bi bi-currency-yen me-2 text-primary"></i>订单金额: ¥[[ service.total_price || 0 ]]</p>
                                                <p class="mb-0"><i class="bi bi-credit-card-fill me-2 text-primary"></i>支付方式: [[ service.payment_method || '未设置' ]]</p>
                                            </div>
                                        </div>

                                        <h6 class="mb-2"><i class="bi bi-basket2-fill me-2"></i>餐品列表</h6>
                                        <div class="meal-items mb-4">
                                            <div v-for="item in service.items" :key="item.id" class="meal-item">
                                                [[ item.name ]] x[[ item.quantity ]]
                                            </div>
                                        </div>



                                        <div class="assignment-form" v-if="showAssignForm === service.id">
                                            <h6 class="mb-3">[[ service.status === 'pending' ? '分配服务人员' : '重新分配服务人员' ]]</h6>
                                            <div class="row g-2">
                                                <div class="col-md-6">
                                                    <select class="form-select" v-model="newAssignedStaff">
                                                        <option value="">选择服务人员</option>
                                                        <option value="1">王小明</option>
                                                        <option value="2">李玲</option>
                                                        <option value="3">赵强</option>
                                                    </select>
                                                </div>
                                                <div class="col-md-6 d-grid">
                                                    <button class="btn btn-primary" @click="confirmReassign(service.id)">确认分配</button>
                                                </div>
                                            </div>
                                        </div>
                                                                                <!-- 订单进度条 -->
                                                                                <div class="order-progress-worker mb-4">
                                                                                    <h6 class="mb-3"><i class="bi bi-clock-history me-2 text-info"></i>订单进度跟踪</h6>
                                                                                    <div class="progress-steps-worker">
                                                                                        <!-- 步骤1：订单提交 -->
                                                                                        <div class="step-worker" :class="getWorkerStepClass(service.status, 1)">
                                                                                            <div class="step-icon-worker">
                                                                                                <i class="bi bi-check-circle-fill"></i>
                                                                                            </div>
                                                                                            <div class="step-content-worker">
                                                                                                <h6>订单已提交</h6>
                                                                                                <small class="text-muted">[[ formatDateTime(service.created_at) ]]</small>
                                                                                            </div>
                                                                                        </div>

                                                                                        <!-- 步骤2：已分配 -->
                                                                                        <div class="step-worker" :class="getWorkerStepClass(service.status, 2)">
                                                                                            <div class="step-icon-worker">
                                                                                                <i class="bi bi-person-check-fill"></i>
                                                                                            </div>
                                                                                            <div class="step-content-worker">
                                                                                                <h6>已分配配送员</h6>
                                                                                                <small v-if="service.staff" class="text-success fw-bold">[[ service.staff ]]</small>
                                                                                                <small v-else class="text-warning">等待分配...</small>
                                                                                            </div>
                                                                                        </div>

                                                                                        <!-- 步骤3：配送中 -->
                                                                                        <div class="step-worker" :class="getWorkerStepClass(service.status, 3)">
                                                                                            <div class="step-icon-worker">
                                                                                                <i class="bi bi-truck"></i>
                                                                                            </div>
                                                                                            <div class="step-content-worker">
                                                                                                <h6>配送中</h6>
                                                                                                <small class="text-primary">配送员正在路上</small>
                                                                                            </div>
                                                                                        </div>

                                                                                        <!-- 步骤4：已送达 -->
                                                                                        <div class="step-worker" :class="getWorkerStepClass(service.status, 4)">
                                                                                            <div class="step-icon-worker">
                                                                                                <i class="bi bi-house-check-fill"></i>
                                                                                            </div>
                                                                                            <div class="step-content-worker">
                                                                                                <h6>已送达</h6>
                                                                                                <small v-if="service.status === 'delivered'" class="text-warning">
                                                                                                    <i class="bi bi-clock-fill me-1"></i>等待用户确认收货
                                                                                                </small>
                                                                                                <small v-else-if="service.status === 'completed'" class="text-success">
                                                                                                    <i class="bi bi-check-circle-fill me-1"></i>用户已确认收货
                                                                                                </small>
                                                                                                <small v-else class="text-muted">等待送达</small>
                                                                                            </div>
                                                                                        </div>

                                                                                        <!-- 步骤5：已完成 -->
                                                                                        <div class="step-worker" :class="getWorkerStepClass(service.status, 5)">
                                                                                            <div class="step-icon-worker">
                                                                                                <i class="bi bi-star-fill"></i>
                                                                                            </div>
                                                                                            <div class="step-content-worker">
                                                                                                <h6>订单完成</h6>
                                                                                                <small class="text-success">
                                                                                                    <i class="bi bi-trophy-fill me-1"></i>服务完成
                                                                                                </small>
                                                                                            </div>
                                                                                        </div>
                                                                                    </div>
                                                                                </div>

                                        <!-- 用户反馈区域 -->
                                        <div class="alert alert-success mt-3" v-if="activeFeedback === service.id && service.status === 'delivered'">
                                            <h6 class="mb-2"><i class="bi bi-chat-square-text-fill me-2"></i>用户反馈</h6>
                                            <p class="mb-1">评分: <i class="bi bi-star-fill text-warning"></i><i class="bi bi-star-fill text-warning"></i><i class="bi bi-star-fill text-warning"></i><i class="bi bi-star-fill text-warning"></i><i class="bi bi-star text-warning"></i></p>
                                            <p class="mb-0">反馈: 服务态度很好，餐食温度适宜，下次还会选择。</p>
                                        </div>
                                    </div>
                                </div>
                            </div>


                        </div>


                    </div>
                </div>
            </div>

            <!-- 继续现有的地图卡片 -->
            <div class="card shadow-sm">
                <div class="card-header">
                    <h5 class="mb-0">社区老年人分布</h5>
                </div>
                <div class="card-body">
                    <div class="map-container">
                        <div id="community-map" style="height: 100%"></div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card shadow-sm mb-4">
                <div class="card-header">
                    <h5 class="mb-0">负责区域老年人</h5>
                </div>
                <div class="card-body">
                    <div class="input-group mb-3">
                        <input type="text" class="form-control" placeholder="搜索老年人..." v-model="searchQuery">
                        <button class="btn btn-outline-secondary" type="button">搜索</button>
                    </div>

                    <div class="elderly-card card mb-3">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="me-3">
                                    <span class="avatar bg-success text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">张</span>
                                </div>
                                <div>
                                    <h6 class="mb-1">张建国 (E01)</h6>
                                    <p class="mb-0 text-muted small">浙江省杭州市余杭区龙湖天街1号 | 78岁</p>
                                </div>
                                <div class="ms-auto">
                                    <div class="status-indicator status-pending" title="有待处理的紧急呼叫"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="elderly-card card mb-3">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="me-3">
                                    <span class="avatar bg-info text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">李</span>
                                </div>
                                <div>
                                    <h6 class="mb-1">李淑兰 (E02)</h6>
                                    <p class="mb-0 text-muted small">浙江省杭州市西湖区西溪路518号 | 75岁</p>
                                </div>
                                <div class="ms-auto">
                                    <div class="status-indicator status-pending" title="有待处理的紧急呼叫"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="elderly-card card mb-3">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="me-3">
                                    <span class="avatar bg-warning text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">王</span>
                                </div>
                                <div>
                                    <h6 class="mb-1">王福海 (E03)</h6>
                                    <p class="mb-0 text-muted small">浙江省杭州市拱墅区湖墅南路88号 | 80岁</p>
                                </div>
                                <div class="ms-auto">
                                    <div class="status-indicator status-responded" title="已响应服务"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="text-center">
                        <button class="btn btn-outline-primary">查看更多老年人</button>
                    </div>
                </div>
            </div>

            <div class="card shadow-sm">
                <div class="card-header">
                    <h5 class="mb-0">健康异常预警</h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning">
                        <div class="d-flex align-items-center">
                            <div class="me-3">
                                <i class="bi bi-exclamation-triangle-fill fs-4"></i>
                            </div>
                            <div>
                                <h6 class="mb-1">李淑兰 (E02)</h6>
                                <p class="mb-0">血糖偏高: 8.2 mmol/L</p>
                                <small class="text-muted">2025-05-06 08:30</small>
                            </div>
                        </div>
                    </div>

                    <div class="alert alert-danger">
                        <div class="d-flex align-items-center">
                            <div class="me-3">
                                <i class="bi bi-heart-fill fs-4"></i>
                            </div>
                            <div>
                                <h6 class="mb-1">王福海 (E03)</h6>
                                <p class="mb-0">心率异常: 95 bpm</p>
                                <small class="text-muted">2025-05-06 09:15</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 紧急事件弹窗模态框 -->
    <div class="modal fade" id="workerEmergencyAlertModal" tabindex="-1" aria-labelledby="workerEmergencyAlertModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content border-danger">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title" id="workerEmergencyAlertModalLabel">
                        <i class="bi bi-exclamation-triangle-fill me-2"></i>紧急呼叫通知
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body p-0">
                    <div v-if="alertEvent">
                        <!-- 紧急事件头部信息 -->
                        <div class="emergency-header bg-danger text-white p-3">
                            <div class="row align-items-center">
                                <div class="col-md-8">
                                    <h4 class="mb-1">
                                        <i class="bi bi-person-exclamation me-2"></i>
                                        [[alertEvent.elderly_name]] 发起了紧急呼叫
                                    </h4>
                                    <div class="d-flex gap-3 text-white-50">
                                        <span><i class="bi bi-tag-fill me-1"></i>类型：[[getEmergencyTypeText(alertEvent.emergency_type)]]</span>
                                        <span><i class="bi bi-clock-fill me-1"></i>[[alertEvent.created_at]]</span>
                                        <span><i class="bi bi-stopwatch me-1"></i>已持续：[[getElapsedTime(alertEvent.created_at)]]</span>
                                    </div>
                                </div>
                                <div class="col-md-4 text-end">
                                    <div class="emergency-priority-badge">
                                        <i class="bi bi-shield-exclamation me-1"></i>
                                        <span class="fw-bold">社区响应</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 主要内容区域 -->
                        <div class="emergency-content p-4">
                            <div class="row">
                                <!-- 左侧：地图和位置信息 -->
                                <div class="col-md-7">
                                    <div class="emergency-map-section">
                                        <h6 class="section-title">
                                            <i class="bi bi-geo-alt-fill text-danger me-2"></i>位置信息与导航
                                        </h6>

                                        <!-- 地图容器 -->
                                        <div class="emergency-map-container mb-3" id="workerEmergencyAlertMap" style="height: 300px; border-radius: 8px; border: 2px solid #dc3545;"></div>

                                        <!-- 位置详情 -->
                                        <div class="location-details">
                                            <div class="row">
                                                <div class="col-6">
                                                    <div class="info-item">
                                                        <label class="text-muted small">详细地址</label>
                                                        <div class="fw-bold">[[alertEvent.address || '获取地址中...']]</div>
                                                    </div>
                                                </div>
                                                <div class="col-6">
                                                    <div class="info-item">
                                                        <label class="text-muted small">GPS坐标</label>
                                                        <div class="fw-bold font-monospace">[[alertEvent.location || '获取中...']]</div>
                                                    </div>
                                                </div>
                                                <div class="col-6 mt-2">
                                                    <div class="info-item">
                                                        <label class="text-muted small">距离社区中心</label>
                                                        <div class="fw-bold text-warning">[[alertEvent.distanceToCenter || '计算中...']]</div>
                                                    </div>
                                                </div>
                                                <div class="col-6 mt-2">
                                                    <div class="info-item">
                                                        <label class="text-muted small">预计到达时间</label>
                                                        <div class="fw-bold text-info">[[alertEvent.estimatedArrival || '计算中...']]</div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 右侧：老人信息和处理建议 -->
                                <div class="col-md-5">
                                    <div class="elderly-info-section">
                                        <h6 class="section-title">
                                            <i class="bi bi-person-fill text-primary me-2"></i>老人档案
                                        </h6>

                                        <div class="elderly-card bg-light p-3 rounded mb-3">
                                            <div class="d-flex align-items-center mb-2">
                                                <div class="elderly-avatar bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 50px; height: 50px;">
                                                    <i class="bi bi-person-fill fs-4"></i>
                                                </div>
                                                <div>
                                                    <h6 class="mb-0">[[alertEvent.elderly_name]]</h6>
                                                    <small class="text-muted">[[alertEvent.elderly_age || '年龄未知']]岁 | [[alertEvent.elderly_gender || '性别未知']]</small>
                                                </div>
                                            </div>
                                            <div class="elderly-contact">
                                                <div class="d-flex justify-content-between mb-1">
                                                    <span class="text-muted small">联系电话</span>
                                                    <span class="fw-bold">[[alertEvent.elderly_phone || '未设置']]</span>
                                                </div>
                                                <div class="d-flex justify-content-between">
                                                    <span class="text-muted small">老人ID</span>
                                                    <span class="fw-bold">[[alertEvent.elderly_id]]</span>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- 健康风险提醒 -->
                                        <div class="health-summary mb-3">
                                            <h6 class="section-title">
                                                <i class="bi bi-heart-pulse text-danger me-2"></i>健康风险提醒
                                            </h6>
                                            <div class="health-alerts">
                                                <div class="alert alert-warning py-2 mb-2" v-if="alertEvent.healthRisks">
                                                    <small><i class="bi bi-exclamation-triangle me-1"></i>[[alertEvent.healthRisks]]</small>
                                                </div>
                                                <div class="vital-signs-mini">
                                                    <div class="row text-center">
                                                        <div class="col-4">
                                                            <div class="vital-mini">
                                                                <div class="text-muted small">血压</div>
                                                                <div class="fw-bold small">[[alertEvent.bloodPressure || '--']]</div>
                                                            </div>
                                                        </div>
                                                        <div class="col-4">
                                                            <div class="vital-mini">
                                                                <div class="text-muted small">心率</div>
                                                                <div class="fw-bold small">[[alertEvent.heartRate || '--']]</div>
                                                            </div>
                                                        </div>
                                                        <div class="col-4">
                                                            <div class="vital-mini">
                                                                <div class="text-muted small">血糖</div>
                                                                <div class="fw-bold small">[[alertEvent.bloodSugar || '--']]</div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- 处理建议 -->
                                        <div class="emergency-suggestions mb-3">
                                            <h6 class="section-title">
                                                <i class="bi bi-lightbulb-fill text-warning me-2"></i>处理建议
                                            </h6>
                                            <div class="suggestions-list">
                                                <div class="suggestion-item py-1" v-for="suggestion in alertEvent.suggestions" :key="suggestion.id">
                                                    <small><i class="bi bi-check-circle me-1 text-success"></i>[[suggestion.text]]</small>
                                                </div>
                                                <div v-if="!alertEvent.suggestions || alertEvent.suggestions.length === 0" class="text-muted small">
                                                    根据紧急类型制定处理方案
                                                </div>
                                            </div>
                                        </div>


                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 底部操作区域 -->
                        <div class="emergency-actions bg-light p-3 border-top">
                            <div class="row align-items-center">
                                <div class="col-md-6">
                                    <div class="response-priority">
                                        <small class="text-muted">优先级：</small>
                                        <span class="badge bg-danger ms-1">
                                            <i class="bi bi-lightning-fill me-1"></i>高优先级
                                        </span>
                                        <small class="text-muted ms-2">建议立即响应</small>
                                    </div>
                                </div>
                                <div class="col-md-6 text-end">
                                    <div class="action-buttons d-flex gap-2 justify-content-end">
                                        <button class="btn btn-success btn-lg" @click="quickWorkerRespond()" :disabled="alertEvent.worker_responded">
                                            <i class="bi bi-shield-check me-2"></i>
                                            [[alertEvent.worker_responded ? '已响应' : '立即响应']]
                                        </button>
                                        <button class="btn btn-outline-primary" @click="showDetailedWorkerResponse">
                                            <i class="bi bi-chat-dots me-1"></i>详细处理
                                        </button>
                                        <button class="btn btn-outline-secondary" data-bs-dismiss="modal">
                                            <i class="bi bi-clock me-1"></i>稍后处理
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 工作人员详细回复模态框 - 高z-index确保在紧急呼叫通知模态框上方 -->
<div class="modal fade" id="workerResponseModal" tabindex="-1" aria-hidden="true" style="z-index: 1060;">
    <div class="modal-dialog modal-lg">
        <div class="modal-content border-primary shadow-lg">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title">
                    <i class="bi bi-chat-heart me-2"></i>详细回复紧急呼叫
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="emergency-info-section" class="emergency-info mb-4" style="display: none;">
                    <div class="alert alert-danger">
                        <h6 class="alert-heading">
                            <i class="bi bi-exclamation-triangle-fill me-2"></i>
                            紧急事件信息
                        </h6>
                        <div class="row">
                            <div class="col-md-6">
                                <p class="mb-1"><strong>老人姓名：</strong><span id="elderly-name-display"></span></p>
                                <p class="mb-1"><strong>紧急类型：</strong><span id="emergency-type-display"></span></p>
                            </div>
                            <div class="col-md-6">
                                <p class="mb-1"><strong>发生时间：</strong><span id="created-time-display"></span></p>
                                <p class="mb-0"><strong>地址：</strong><span id="address-display"></span></p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="response-form">
                    <label for="workerResponseMessage" class="form-label">
                        <i class="bi bi-chat-text me-2"></i>回复消息
                    </label>
                    <textarea
                        class="form-control"
                        id="workerResponseMessage"
                        v-model="responseMessage"
                        rows="5"
                        placeholder="请输入您的详细回复消息，告知老人您的处理方案和预计到达时间..."
                        maxlength="500">
                    </textarea>
                    <div class="form-text">
                        <i class="bi bi-info-circle me-1"></i>
                        建议包含：安抚话语、处理方案、预计到达时间、联系方式等信息
                    </div>
                </div>

                <div class="quick-responses mt-3">
                    <label class="form-label">
                        <i class="bi bi-lightning me-2"></i>快速回复模板
                    </label>
                    <div class="d-flex flex-wrap gap-2">
                        <button type="button" class="btn btn-outline-primary btn-sm" onclick="setQuickResponse('我已收到您的紧急呼叫，正在立即前往您的位置，预计5-10分钟内到达。请保持冷静，如有需要请拨打120。')">
                            立即前往
                        </button>
                        <button type="button" class="btn btn-outline-primary btn-sm" onclick="setQuickResponse('我已联系相关医疗人员，同时正在前往现场。请不要移动，保持当前姿势，我们很快就到。')">
                            医疗协助
                        </button>
                        <button type="button" class="btn btn-outline-primary btn-sm" onclick="setQuickResponse('我已收到您的求助信息，正在协调相关资源为您提供帮助。请稍等片刻，我会尽快联系您。')">
                            协调资源
                        </button>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="bi bi-x-circle me-1"></i>取消
                </button>
                <button type="button" class="btn btn-primary" onclick="submitWorkerResponseDirect()" id="submit-response-btn">
                    <i class="bi bi-send me-1"></i>发送回复
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    new Vue({
        el: '#worker-app',
        delimiters: ['[[', ']]'],
        data: {
            searchQuery: '',
            emergencyCalls: [], // 改为动态加载
            // 新增数据
            showResolved: false,
            activeService: null,
            showAssignForm: null,
            newAssignedStaff: '',
            selectedStaff: '',
            activeFeedback: null,
            mealServices: [], // 将从API动态加载
            isLoadingMealServices: false,
            mealServicesStats: {
                total: 0,
                pending: 0,
                assigned: 0,
                delivering: 0,
                delivered: 0,
                completed: 0
            },
            lastOrderCount: 0, // 用于检测新订单
            orderPollingInterval: null, // 订单状态轮询定时器
            previousMealServices: [], // 用于比较订单状态变化
            map: null,
            workers: [
                { name: '刘强', region: 'A' },
                { name: '王丽', region: 'B' }
            ],
            // 紧急事件监控相关
            currentUser: null,
            emergencyEvents: [],
            alertEvent: null,
            emergencyAlertModalInstance: null,
            shownAlerts: [], // 记录已显示过弹窗的事件ID
            isLoadingEmergencyEvents: false, // 紧急事件加载状态
            currentView: 'mealServices', // 当前视图
            // 工作人员详细回复相关
            selectedEvent: null,
            responseMessage: '',
            workerResponseModalInstance: null
        },
        mounted() {
            // 延迟初始化地图，确保DOM完全加载
            this.$nextTick(() => {
                setTimeout(() => {
                    this.initMap();
                }, 500);
            });

            // 获取当前用户信息
            this.getCurrentUser();

            this.loadMealOrders();

            // 启动订单状态轮询
            this.startOrderStatusPolling();

            // 启动紧急事件监控
            this.initEmergencyMonitoring();

            // 初始加载紧急事件
            this.loadEmergencyEvents();

            // 初始化紧急事件弹窗模态框
            this.$nextTick(() => {
                const modalElement = document.getElementById('workerEmergencyAlertModal');
                if (modalElement) {
                    this.emergencyAlertModalInstance = new bootstrap.Modal(modalElement);
                }

                // 初始化工作人员详细回复模态框
                const workerResponseModalElement = document.getElementById('workerResponseModal');
                if (workerResponseModalElement) {
                    this.workerResponseModalInstance = new bootstrap.Modal(workerResponseModalElement);
                }
            });
        },
        beforeDestroy() {
            // 清理轮询定时器
            this.stopOrderStatusPolling();
        },
        methods: {
            initMap() {
                console.log('开始初始化地图...');

                // 检查地图容器是否存在
                const mapContainer = document.getElementById('community-map');
                if (!mapContainer) {
                    console.error('地图容器不存在');
                    return;
                }

                // 等待高德地图API加载完成
                if (typeof AMap === 'undefined') {
                    console.error('高德地图API未加载，1秒后重试...');
                    setTimeout(() => this.initMap(), 1000);
                    return;
                }

                try {
                    console.log('正在创建地图实例...');
                    // 初始化高德地图 - 杭州坐标
                    const map = new AMap.Map('community-map', {
                        zoom: 13,
                        center: [120.1551, 30.2741] // 杭州市中心坐标
                    });

                    this.map = map;
                    console.log('地图实例创建成功');

                    // 直接添加标记，不等待complete事件
                    setTimeout(() => {
                        console.log('开始添加标记');
                        this.addMarkersToMap();
                    }, 1000);

                } catch (error) {
                    console.error('地图初始化失败:', error);
                    // 显示错误信息给用户
                    const mapContainer = document.getElementById('community-map');
                    if (mapContainer) {
                        mapContainer.innerHTML = `
                            <div class="d-flex align-items-center justify-content-center h-100">
                                <div class="text-center">
                                    <i class="bi bi-exclamation-triangle fs-1 text-warning"></i>
                                    <p class="mt-2">地图加载失败，请刷新页面重试</p>
                                </div>
                            </div>
                        `;
                    }
                }
            },

            // 移除loadElderlyLocations方法，改为直接使用模拟数据

            addMarkersToMap() {
                if (!this.map) {
                    console.error('地图对象不存在');
                    return;
                }

                try {
                    // 杭州老年人模拟位置数据 - 使用数组格式的坐标
                    const elderlyMarkers = [
                        {
                            position: [120.1551, 30.2741], // 杭州市中心
                            title: '张建国 (E01)',
                            name: '张建国',
                            elderly_id: 'E01',
                            address: '浙江省杭州市余杭区龙湖天街1号',
                            age: '78岁',
                            phone: '138****1234'
                        },
                        {
                            position: [120.1300, 30.2500], // 西湖区
                            title: '李淑兰 (E02)',
                            name: '李淑兰',
                            elderly_id: 'E02',
                            address: '浙江省杭州市西湖区西溪路518号',
                            age: '75岁',
                            phone: '139****5678'
                        },
                        {
                            position: [120.1800, 30.3000], // 拱墅区
                            title: '王福海 (E03)',
                            name: '王福海',
                            elderly_id: 'E03',
                            address: '浙江省杭州市拱墅区湖墅南路88号',
                            age: '80岁',
                            phone: '137****9012'
                        }
                    ];

                    console.log('开始添加标记，共', elderlyMarkers.length, '个');

                    // 添加老年人位置标记
                    elderlyMarkers.forEach((elderly, index) => {
                        try {
                            const marker = new AMap.Marker({
                                position: elderly.position,
                                title: elderly.title
                            });

                            // 将标记添加到地图
                            marker.setMap(this.map);

                            // 添加信息窗口
                            const infoWindow = new AMap.InfoWindow({
                                content: `
                                    <div style="padding: 10px;">
                                        <h6>${elderly.name} (${elderly.elderly_id})</h6>
                                        <p><strong>地址:</strong> ${elderly.address}</p>
                                        <p><strong>年龄:</strong> ${elderly.age}</p>
                                        <p><strong>电话:</strong> ${elderly.phone}</p>
                                        <p><strong>状态:</strong> 正常</p>
                                    </div>
                                `
                            });

                            marker.on('click', () => {
                                infoWindow.open(this.map, marker.getPosition());
                            });

                            console.log(`标记 ${index + 1} 添加成功:`, elderly.name);
                        } catch (error) {
                            console.error(`添加标记 ${index + 1} 失败:`, error);
                        }
                    });

                    console.log('所有标记添加完成');

                } catch (error) {
                    console.error('添加标记失败:', error);
                }
            },

            // 移除updateElderlyList方法，因为不再需要从API获取数据

            // 加载订餐订单数据
            async loadMealOrders() {
                if (this.isLoadingMealServices) return; // 防止重复请求

                try {
                    this.isLoadingMealServices = true;
                    console.log('正在加载订餐订单数据...');

                    const response = await fetch('/api/worker/meal_orders');

                    // 检查网络连接
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }

                    const result = await response.json();

                    if (result.success) {
                        // 检测新订单
                        const newOrderCount = result.total_count || 0;
                        if (this.lastOrderCount > 0 && newOrderCount > this.lastOrderCount) {
                            this.showNewOrderNotification(newOrderCount - this.lastOrderCount);
                        }
                        this.lastOrderCount = newOrderCount;

                        // 保存当前已完成的订单，避免被覆盖
                        const currentCompletedOrders = this.mealServices.filter(order => order.status === 'completed');

                        // 更新订单数据，但保持已完成订单的状态
                        const newServices = result.meal_services || [];

                        // 合并数据：新数据中的非完成订单 + 当前已完成的订单
                        this.mealServices = newServices.map(newOrder => {
                            // 检查是否有对应的已完成订单
                            const completedOrder = currentCompletedOrders.find(completed => completed.id === newOrder.id);
                            if (completedOrder) {
                                // 如果本地已经是完成状态，保持完成状态
                                console.log(`保持订单 ${newOrder.id} 的完成状态`);
                                return completedOrder;
                            }
                            return newOrder;
                        });

                        // 添加新完成的订单到已完成列表
                        const newlyCompleted = newServices.filter(order =>
                            order.status === 'completed' &&
                            !currentCompletedOrders.some(completed => completed.id === order.id)
                        );

                        if (newlyCompleted.length > 0) {
                            newlyCompleted.forEach(order => {
                                console.log(`订单 ${order.id} 新完成，添加到完成列表`);
                                this.showOrderCompletedNotification(order);
                            });
                        }

                        this.mealServicesStats = {
                            total: result.total_count || 0,
                            pending: result.pending_count || 0,
                            assigned: result.assigned_count || 0,
                            delivering: result.delivering_count || 0,
                            delivered: result.delivered_count || 0,
                            completed: result.completed_count || 0
                        };

                        console.log(`成功加载 ${this.mealServices.length} 个订餐订单`);

                        // 如果有新的待分配订单，显示提醒
                        if (result.pending_count > 0) {
                            console.log(`有 ${result.pending_count} 个待分配的订餐订单`);
                        }

                        // 检查是否有订单状态变化需要提醒
                        this.checkOrderStatusChanges();
                    } else {
                        console.error('加载订餐订单失败:', result.error);
                        // 不更新数据，保持当前状态
                    }
                } catch (error) {
                    console.error('加载订餐订单数据失败:', error);
                    // 网络错误时不更新数据，避免状态回退
                    if (error.message.includes('ERR_CONNECTION_REFUSED') || error.message.includes('Failed to fetch')) {
                        console.warn('网络连接失败，保持当前订单状态');
                    }
                } finally {
                    this.isLoadingMealServices = false;
                }
            },

            // 获取当前用户信息
            async getCurrentUser() {
                try {
                    const response = await fetch('/api/auth/current_user');
                    if (response.ok) {
                        const result = await response.json();
                        this.currentUser = result.user;
                        console.log('当前工作人员用户:', this.currentUser);
                    }
                } catch (error) {
                    console.error('获取用户信息失败:', error);
                }
            },

            // 初始化紧急事件监控
            initEmergencyMonitoring() {
                // 定期检查新的紧急事件
                setInterval(() => {
                    // 即使没有登录也要检查紧急事件
                    this.checkForNewEmergencyEvents();
                }, 3000); // 每3秒检查一次
            },

            // 检查新的紧急事件
            async checkForNewEmergencyEvents() {
                try {
                    console.log('🔍 工作人员端检查新的紧急事件...');

                    const response = await fetch('/api/emergency_event/active?user_type=worker&user_id=' + (this.currentUser?.id || 'W_A01'));
                    const result = await response.json();

                    if (result.events) {
                        console.log('📋 工作人员端收到事件:', result.events);

                        // 更新紧急事件列表
                        this.emergencyEvents = result.events;

                        // 更新静态显示的紧急呼叫列表
                        this.emergencyCalls = result.events.map(event => ({
                            id: event.event_id,
                            elderly_id: event.elderly_id,
                            name: event.elderly_name,
                            status: event.worker_responded ? '已响应' : '待处理',
                            address: event.address,
                            time: event.created_at,
                            emergency_type: event.emergency_type
                        }));

                        // 查找待处理且未响应的事件
                        const pendingEvents = result.events.filter(event =>
                            event.status === '待处理' &&
                            !event.worker_responded
                        );

                        // 检查是否有新的待处理事件（不在已显示的弹窗记录中）
                        const newEvents = pendingEvents.filter(event =>
                            !this.shownAlerts.includes(event.event_id)
                        );

                        if (newEvents.length > 0) {
                            console.log('🚨 工作人员端发现新的紧急事件，显示弹窗');
                            const latestEvent = newEvents[0];

                            // 记录已显示的弹窗
                            this.shownAlerts.push(latestEvent.event_id);

                            // 显示弹窗
                            this.alertEvent = latestEvent;
                            this.showEmergencyAlert();

                            // 播放提示音
                            this.playAlertSound();
                        }
                    }
                } catch (error) {
                    console.error('❌ 工作人员端检查紧急事件失败:', error);
                }
            },

            // 显示紧急事件弹窗
            showEmergencyAlert() {
                if (this.alertEvent && this.emergencyAlertModalInstance) {
                    // 丰富事件数据
                    this.alertEvent = this.enrichWorkerEmergencyEventData(this.alertEvent);

                    this.emergencyAlertModalInstance.show();

                    // 在弹窗显示后初始化地图
                    this.$nextTick(() => {
                        this.initWorkerEmergencyAlertMap();
                    });
                }
            },

            // 播放提示音
            playAlertSound() {
                try {
                    const audio = new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT');
                    audio.play().catch(e => console.log('无法播放提示音:', e));
                } catch (error) {
                    console.log('播放提示音失败:', error);
                }
            },

            // 获取紧急类型文本
            getEmergencyTypeText(type) {
                const typeMap = {
                    'medical': '医疗紧急',
                    'fall': '跌倒求助',
                    'help': '一般求助'
                };
                return typeMap[type] || type;
            },

            // 手动加载紧急事件
            async loadEmergencyEvents() {
                this.isLoadingEmergencyEvents = true;
                try {
                    console.log('🔄 手动刷新紧急事件列表...');
                    await this.checkForNewEmergencyEvents();
                } catch (error) {
                    console.error('加载紧急事件失败:', error);
                    alert('加载紧急事件失败，请重试');
                } finally {
                    this.isLoadingEmergencyEvents = false;
                }
            },

            // 快速响应（从弹窗）
            async quickWorkerRespond() {
                if (this.alertEvent) {
                    await this.respondToCall(this.alertEvent.event_id);

                    // 关闭弹窗
                    if (this.emergencyAlertModalInstance) {
                        this.emergencyAlertModalInstance.hide();
                    }
                }
            },

            // 响应紧急呼叫
            async respondToCall(callId) {
                try {
                    console.log('🚀 工作人员响应紧急呼叫:', callId);

                    const response = await fetch('/api/emergency_event/respond', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            event_id: callId,
                            user_type: 'worker',
                            user_id: this.currentUser?.id || 'W_A01',
                            response_note: '社区工作人员已响应，正在前往现场处理。'
                        })
                    });

                    const result = await response.json();

                    if (result.success) {
                        alert('响应成功！已记录您的响应，请立即前往现场处理。');

                        // 更新本地状态
                        const call = this.emergencyCalls.find(c => c.id === callId);
                        if (call) {
                            call.status = '已响应';
                        }

                        // 刷新事件列表
                        this.checkForNewEmergencyEvents();
                    } else {
                        alert('响应失败：' + result.error);
                    }
                } catch (error) {
                    console.error('响应紧急呼叫失败:', error);
                    alert('响应失败，请重试');
                }
            },

            // 新增方法
            showDetails(serviceId) {
                // 显示/隐藏服务详情
                this.activeService = this.activeService === serviceId ? null : serviceId;
                this.showAssignForm = null; // 关闭分配表单
                this.activeFeedback = null; // 关闭反馈
            },

            reassignService(serviceId) {
                // 显示分配服务人员表单
                this.activeService = serviceId;
                this.showAssignForm = serviceId;
                this.newAssignedStaff = '';
            },

            async confirmReassign(serviceId) {
                // 确认分配服务人员
                if (!this.newAssignedStaff) {
                    alert('请选择服务人员');
                    return;
                }

                try {
                    // 获取服务人员名称
                    let staffName = '';
                    switch(this.newAssignedStaff) {
                        case '1': staffName = '王小明'; break;
                        case '2': staffName = '李玲'; break;
                        case '3': staffName = '赵强'; break;
                    }

                    // 调用后端API分配服务人员
                    const response = await fetch('/api/worker/assign_meal_order', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            order_id: serviceId,
                            assigned_staff: this.newAssignedStaff
                        })
                    });

                    const result = await response.json();

                    if (response.ok && result.success) {
                        alert(`已将订单分配给 ${result.staff_name}`);

                        // 更新本地数据
                        const service = this.mealServices.find(s => s.id === serviceId);
                        if (service) {
                            service.status = 'assigned';
                            service.staff = staffName;
                        }

                        this.showAssignForm = null;
                        this.newAssignedStaff = '';

                        // 重新加载订单数据以确保同步
                        await this.loadMealOrders();
                    } else {
                        throw new Error(result.error || '分配失败');
                    }
                } catch (error) {
                    console.error('分配服务人员失败:', error);
                    alert(`分配失败: ${error.message}`);
                }
            },

            async markAsDelivered(serviceId) {
                // 标记送餐已完成
                try {
                    const response = await fetch('/api/worker/update_meal_order_status', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            order_id: serviceId,
                            status: 'delivered'
                        })
                    });

                    const result = await response.json();

                    if (response.ok && result.success) {
                        // 更新本地数据
                        const service = this.mealServices.find(s => s.id === serviceId);
                        if (service) {
                            service.status = 'delivered';
                            alert(`已将 ${service.name} 的送餐服务标记为已送达`);
                        }

                        // 重新加载订单数据以确保同步
                        await this.loadMealOrders();
                    } else {
                        throw new Error(result.error || '状态更新失败');
                    }
                } catch (error) {
                    console.error('更新订单状态失败:', error);
                    alert(`状态更新失败: ${error.message}`);
                }
            },

            viewFeedback(serviceId) {
                // 查看用户反馈
                this.activeService = serviceId;
                this.activeFeedback = serviceId;
                this.showAssignForm = null;
            },

            async assignAllToStaff() {
                // 批量分配服务人员
                if (!this.selectedStaff) {
                    alert('请选择服务人员');
                    return;
                }

                try {
                    // 获取服务人员名称
                    let staffName = '';
                    switch(this.selectedStaff) {
                        case '1': staffName = '王小明'; break;
                        case '2': staffName = '李玲'; break;
                        case '3': staffName = '赵强'; break;
                    }

                    // 获取所有待分配的订单
                    const pendingOrders = this.mealServices.filter(service => service.status === 'pending');

                    if (pendingOrders.length === 0) {
                        alert('没有待分配的送餐服务');
                        return;
                    }

                    // 批量分配
                    let successCount = 0;
                    let failCount = 0;

                    for (const order of pendingOrders) {
                        try {
                            const response = await fetch('/api/worker/assign_meal_order', {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json',
                                },
                                body: JSON.stringify({
                                    order_id: order.id,
                                    assigned_staff: this.selectedStaff
                                })
                            });

                            const result = await response.json();
                            if (response.ok && result.success) {
                                successCount++;
                            } else {
                                failCount++;
                                console.error(`分配订单 ${order.id} 失败:`, result.error);
                            }
                        } catch (error) {
                            failCount++;
                            console.error(`分配订单 ${order.id} 失败:`, error);
                        }
                    }

                    // 显示结果
                    if (successCount > 0) {
                        alert(`已将 ${successCount} 项待分配的送餐服务分配给 ${staffName}${failCount > 0 ? `，${failCount} 项分配失败` : ''}`);
                    } else {
                        alert('批量分配失败，请稍后重试');
                    }

                    this.selectedStaff = '';

                    // 重新加载订单数据
                    await this.loadMealOrders();
                } catch (error) {
                    console.error('批量分配失败:', error);
                    alert(`批量分配失败: ${error.message}`);
                }
            },



            // 辅助方法：获取头像背景色类
            getAvatarClass(status) {
                switch(status) {
                    case 'pending_assignment':
                    case 'pending': return 'bg-warning';
                    case 'assigned': return 'bg-primary';
                    case 'delivering': return 'bg-info';
                    case 'delivered': return 'bg-success';
                    case 'completed': return 'bg-secondary';
                    default: return 'bg-secondary';
                }
            },

            // 辅助方法：格式化日期时间
            formatDateTime(dateTimeStr) {
                if (!dateTimeStr) return '未设置';
                try {
                    const date = new Date(dateTimeStr);
                    return date.toLocaleString('zh-CN', {
                        year: 'numeric',
                        month: '2-digit',
                        day: '2-digit',
                        hour: '2-digit',
                        minute: '2-digit'
                    });
                } catch (error) {
                    return dateTimeStr;
                }
            },

            // 计算事件持续时间
            getElapsedTime(createdAt) {
                try {
                    const eventTime = new Date(createdAt);
                    const now = new Date();
                    const diffMs = now - eventTime;
                    const diffMins = Math.floor(diffMs / 60000);
                    const diffHours = Math.floor(diffMins / 60);

                    if (diffHours > 0) {
                        return `${diffHours}小时${diffMins % 60}分钟`;
                    } else {
                        return `${diffMins}分钟`;
                    }
                } catch (error) {
                    return '计算中...';
                }
            },

            // 初始化工作人员紧急事件弹窗地图
            initWorkerEmergencyAlertMap() {
                if (!this.alertEvent || !this.alertEvent.location) {
                    console.log('没有位置信息，无法初始化地图');
                    return;
                }

                try {
                    const mapContainer = document.getElementById('workerEmergencyAlertMap');
                    if (!mapContainer) {
                        console.log('地图容器不存在');
                        return;
                    }

                    // 解析GPS坐标
                    const [lat, lng] = this.alertEvent.location.split(',').map(Number);

                    // 清除现有地图
                    if (this.workerEmergencyAlertMap) {
                        this.workerEmergencyAlertMap.destroy();
                    }

                    // 创建地图实例
                    this.workerEmergencyAlertMap = new AMap.Map('workerEmergencyAlertMap', {
                        zoom: 16,
                        center: [lng, lat],
                        mapStyle: 'amap://styles/normal'
                    });

                    // 添加老人位置标记
                    const elderlyMarker = new AMap.Marker({
                        position: [lng, lat],
                        title: `${this.alertEvent.elderly_name}的紧急位置`,
                        icon: new AMap.Icon({
                            size: new AMap.Size(40, 50),
                            image: 'https://webapi.amap.com/theme/v1.3/markers/n/mark_r.png',
                            imageOffset: new AMap.Pixel(0, 0)
                        })
                    });

                    this.workerEmergencyAlertMap.add(elderlyMarker);

                    // 添加社区中心标记（模拟）
                    const centerLng = lng + 0.005;
                    const centerLat = lat + 0.005;

                    const centerMarker = new AMap.Marker({
                        position: [centerLng, centerLat],
                        title: '社区服务中心',
                        icon: new AMap.Icon({
                            size: new AMap.Size(35, 45),
                            image: 'https://webapi.amap.com/theme/v1.3/markers/n/mark_b.png',
                            imageOffset: new AMap.Pixel(0, 0)
                        })
                    });

                    this.workerEmergencyAlertMap.add(centerMarker);

                    // 添加紧急区域圆圈
                    const emergencyCircle = new AMap.Circle({
                        center: [lng, lat],
                        radius: 100,
                        fillColor: 'rgba(255, 77, 79, 0.2)',
                        strokeColor: '#FF4D4F',
                        strokeWeight: 3,
                        strokeStyle: 'dashed'
                    });

                    this.workerEmergencyAlertMap.add(emergencyCircle);

                    // 计算距离和预计到达时间
                    this.calculateWorkerDistanceAndTime(lng, lat, centerLng, centerLat);

                } catch (error) {
                    console.error('初始化工作人员紧急事件地图失败:', error);
                }
            },

            // 计算工作人员距离和预计到达时间
            calculateWorkerDistanceAndTime(elderlyLng, elderlyLat, centerLng, centerLat) {
                // 计算距离（简单的直线距离计算）
                const distance = this.calculateDistance(elderlyLat, elderlyLng, centerLat, centerLng);
                this.$set(this.alertEvent, 'distanceToCenter', `约${distance.toFixed(1)}公里`);

                // 估算到达时间（假设平均速度30km/h）
                const estimatedMinutes = Math.ceil((distance / 30) * 60);
                this.$set(this.alertEvent, 'estimatedArrival', `约${estimatedMinutes}分钟`);
            },

            // 计算两点间距离
            calculateDistance(lat1, lng1, lat2, lng2) {
                const R = 6371; // 地球半径（公里）
                const dLat = (lat2 - lat1) * Math.PI / 180;
                const dLng = (lng2 - lng1) * Math.PI / 180;
                const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
                          Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
                          Math.sin(dLng/2) * Math.sin(dLng/2);
                const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
                return R * c;
            },

            // 丰富工作人员紧急事件数据
            enrichWorkerEmergencyEventData(event) {
                // 添加模拟的老人详细信息
                this.$set(event, 'elderly_age', 75);
                this.$set(event, 'elderly_gender', '男');
                this.$set(event, 'elderly_phone', '138****5678');

                // 添加健康风险信息
                this.$set(event, 'healthRisks', '高血压、糖尿病患者，请注意血压和血糖监测');
                this.$set(event, 'bloodPressure', '146/90');
                this.$set(event, 'heartRate', '78');
                this.$set(event, 'bloodSugar', '7.6');

                // 添加处理建议
                const suggestions = this.getEmergencySuggestions(event.emergency_type);
                this.$set(event, 'suggestions', suggestions);

                return event;
            },

            // 根据紧急类型获取处理建议
            getEmergencySuggestions(emergencyType) {
                const suggestionMap = {
                    'medical': [
                        { id: 1, text: '立即联系120急救中心' },
                        { id: 2, text: '确认老人意识状态和生命体征' },
                        { id: 3, text: '通知家属和主治医生' },
                        { id: 4, text: '准备老人的病历和药物清单' }
                    ],
                    'fall': [
                        { id: 1, text: '不要随意移动老人，评估伤情' },
                        { id: 2, text: '检查是否有骨折或外伤' },
                        { id: 3, text: '如有必要，联系120急救' },
                        { id: 4, text: '安抚老人情绪，保持现场安全' }
                    ],
                    'help': [
                        { id: 1, text: '了解具体需要帮助的事项' },
                        { id: 2, text: '评估紧急程度和所需资源' },
                        { id: 3, text: '联系相关服务人员' },
                        { id: 4, text: '跟进处理结果' }
                    ]
                };

                return suggestionMap[emergencyType] || [
                    { id: 1, text: '立即前往现场了解情况' },
                    { id: 2, text: '联系相关部门协助处理' }
                ];
            },

            // 测试详细回复功能
            testDetailedResponse() {
                console.log('🧪 测试详细回复功能');

                // 创建测试事件数据
                const testEvent = {
                    event_id: 'TEST_001',
                    elderly_name: '张建国',
                    elderly_id: 'E01',
                    emergency_type: 'medical',
                    created_at: '2024-01-15 14:30:00',
                    address: '浙江省杭州市余杭区龙湖天街1号'
                };

                console.log('🧪 设置测试事件:', testEvent);
                this.selectedEvent = testEvent;
                this.responseMessage = '';

                // 直接更新DOM元素内容
                this.updateEmergencyInfoDisplay();

                // 清空textarea
                const textarea = document.getElementById('workerResponseMessage');
                if (textarea) {
                    textarea.value = '';
                }

                if (this.workerResponseModalInstance) {
                    this.workerResponseModalInstance.show();
                    console.log('✅ 测试模态框已显示');
                } else {
                    console.error('❌ workerResponseModalInstance 未初始化');
                }
            },

            // 显示详细工作人员响应
            showDetailedWorkerResponse() {
                console.log('🔧 显示工作人员详细回复模态框');
                console.log('🔧 当前alertEvent:', this.alertEvent);

                this.selectedEvent = this.alertEvent;
                this.responseMessage = '';

                // 直接更新DOM元素内容
                this.updateEmergencyInfoDisplay();

                console.log('🔧 设置selectedEvent:', this.selectedEvent);

                if (this.workerResponseModalInstance) {
                    this.workerResponseModalInstance.show();
                } else {
                    console.error('❌ workerResponseModalInstance 未初始化');
                }
            },

            // 更新紧急事件信息显示
            updateEmergencyInfoDisplay() {
                const infoSection = document.getElementById('emergency-info-section');
                const elderlyNameDisplay = document.getElementById('elderly-name-display');
                const emergencyTypeDisplay = document.getElementById('emergency-type-display');
                const createdTimeDisplay = document.getElementById('created-time-display');
                const addressDisplay = document.getElementById('address-display');

                if (this.selectedEvent && infoSection) {
                    // 显示信息区域
                    infoSection.style.display = 'block';

                    // 更新各个字段
                    if (elderlyNameDisplay) {
                        elderlyNameDisplay.textContent = this.selectedEvent.elderly_name || '未知';
                    }
                    if (emergencyTypeDisplay) {
                        emergencyTypeDisplay.textContent = this.getEmergencyTypeText(this.selectedEvent.emergency_type);
                    }
                    if (createdTimeDisplay) {
                        createdTimeDisplay.textContent = this.selectedEvent.created_at || '未知时间';
                    }
                    if (addressDisplay) {
                        addressDisplay.textContent = this.selectedEvent.address || '位置信息获取中...';
                    }
                } else if (infoSection) {
                    // 隐藏信息区域
                    infoSection.style.display = 'none';
                }
            },

            // 提交工作人员自定义响应
            async submitWorkerResponse() {
                console.log('🚀 提交工作人员详细回复');
                console.log('🚀 回复消息:', this.responseMessage);
                console.log('🚀 选中事件:', this.selectedEvent);

                if (!this.responseMessage.trim()) {
                    alert('请输入回复消息');
                    return;
                }

                if (!this.selectedEvent || !this.selectedEvent.event_id) {
                    alert('未找到事件信息，请重试');
                    console.error('❌ selectedEvent 或 event_id 为空');
                    return;
                }

                await this.respondToWorkerEvent(this.selectedEvent.event_id, this.responseMessage);

                // 关闭详细回复模态框
                if (this.workerResponseModalInstance) {
                    this.workerResponseModalInstance.hide();
                }

                // 自动关闭紧急呼叫通知弹窗
                if (this.emergencyAlertModalInstance) {
                    this.emergencyAlertModalInstance.hide();
                    console.log('✅ 工作人员详细回复提交后，自动关闭紧急呼叫通知弹窗');
                }
            },

            // 工作人员响应紧急事件
            async respondToWorkerEvent(eventId, message) {
                try {
                    const response = await fetch('/api/emergency_event/respond', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            event_id: eventId,
                            user_type: 'worker',
                            user_id: this.currentUser?.id || 'W_A01',
                            response_note: message
                        })
                    });

                    const result = await response.json();

                    if (result.success) {
                        alert('响应成功！您的回复已发送给老人');
                        this.checkForNewEmergencyEvents(); // 刷新列表
                    } else {
                        alert('响应失败：' + result.error);
                    }
                } catch (error) {
                    console.error('响应失败:', error);
                    alert('响应失败，请重试');
                }
            },

            // 直接响应紧急事件（用于全局函数调用）
            async respondToWorkerEventDirect(eventId, message) {
                try {
                    console.log('🚀 直接响应紧急事件:', eventId, message);

                    const response = await fetch('/api/emergency_event/respond', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            event_id: eventId,
                            user_type: 'worker',
                            user_id: this.currentUser?.id || 'W_A01',
                            response_note: message
                        })
                    });

                    const result = await response.json();

                    if (result.success) {
                        alert('响应成功！您的回复已发送给老人');

                        // 关闭详细回复模态框
                        if (this.workerResponseModalInstance) {
                            this.workerResponseModalInstance.hide();
                        }

                        // 自动关闭紧急呼叫通知弹窗
                        if (this.emergencyAlertModalInstance) {
                            this.emergencyAlertModalInstance.hide();
                            console.log('✅ 工作人员详细回复提交后，自动关闭紧急呼叫通知弹窗');
                        }

                        // 清空回复消息
                        this.responseMessage = '';
                        const textarea = document.getElementById('workerResponseMessage');
                        if (textarea) {
                            textarea.value = '';
                        }

                        this.checkForNewEmergencyEvents(); // 刷新列表
                    } else {
                        alert('响应失败：' + result.error);
                    }
                } catch (error) {
                    console.error('响应失败:', error);
                    alert('响应失败，请重试');
                }
            },

            // 显示新订单通知
            showNewOrderNotification(count) {
                // 创建通知元素
                const notification = document.createElement('div');
                notification.className = 'alert alert-success alert-dismissible fade show position-fixed';
                notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
                notification.innerHTML = `
                    <i class="bi bi-bell-fill me-2"></i>
                    <strong>新订单提醒</strong><br>
                    收到 ${count} 个新的订餐订单，请及时处理！
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                `;

                document.body.appendChild(notification);

                // 3秒后自动消失
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 3000);

                console.log(`🔔 新订单提醒：收到 ${count} 个新订单`);
            },

            // 显示订单完成通知
            showOrderCompletedNotification(order) {
                // 创建通知元素
                const notification = document.createElement('div');
                notification.className = 'alert alert-info alert-dismissible fade show position-fixed';
                notification.style.cssText = 'top: 80px; right: 20px; z-index: 9999; min-width: 350px;';
                notification.innerHTML = `
                    <i class="bi bi-check-circle-fill me-2"></i>
                    <strong>订单完成通知</strong><br>
                    ${order.name} 的订单 ${order.id} 已确认收货完成！
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                `;

                document.body.appendChild(notification);

                // 5秒后自动消失
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 5000);

                console.log(`✅ 订单完成通知：${order.name} 的订单 ${order.id} 已完成`);
            },

            // 检查订单状态变化
            checkOrderStatusChanges() {
                // 检查是否有新完成的订单（从delivered变为completed）
                if (this.previousMealServices && this.previousMealServices.length > 0) {
                    const previousCompleted = this.previousMealServices.filter(order => order.status === 'completed');
                    const currentCompleted = this.mealServices.filter(order => order.status === 'completed');

                    // 找出新完成的订单
                    const newlyCompleted = currentCompleted.filter(current =>
                        !previousCompleted.some(prev => prev.id === current.id)
                    );

                    if (newlyCompleted.length > 0) {
                        newlyCompleted.forEach(order => {
                            this.showOrderCompletedNotification(order);
                        });
                    }

                    // 检查是否有新的已送达订单
                    const previousDelivered = this.previousMealServices.filter(order => order.status === 'delivered');
                    const currentDelivered = this.mealServices.filter(order => order.status === 'delivered');

                    const newlyDelivered = currentDelivered.filter(current =>
                        !previousDelivered.some(prev => prev.id === current.id)
                    );

                    if (newlyDelivered.length > 0) {
                        console.log(`📦 有 ${newlyDelivered.length} 个新订单已送达，等待用户确认收货`);
                    }
                }

                // 保存当前状态用于下次比较
                this.previousMealServices = JSON.parse(JSON.stringify(this.mealServices));
            },

            // 开始轮询订单状态更新
            startOrderStatusPolling() {
                // 清除之前的轮询
                if (this.orderPollingInterval) {
                    clearInterval(this.orderPollingInterval);
                }

                // 开始新的轮询，每5秒检查一次订单状态
                this.orderPollingInterval = setInterval(async () => {
                    if (this.currentView === 'mealServices') {
                        // 检查是否还有未完成的订单
                        const activeOrders = this.mealServices.filter(order =>
                            order.status !== 'completed'
                        );

                        if (activeOrders.length > 0) {
                            // 还有未完成的订单，继续轮询
                            try {
                                await this.loadMealOrders();
                            } catch (error) {
                                console.warn('轮询过程中发生错误，将在下次轮询时重试:', error);
                            }
                        } else {
                            // 所有订单都已完成，减少轮询频率
                            console.log('所有订单已完成，保持当前状态');
                        }
                    }
                }, 5000);

                console.log('已启动订单状态轮询，每5秒更新一次');
            },

            // 停止轮询订单状态更新
            stopOrderStatusPolling() {
                if (this.orderPollingInterval) {
                    clearInterval(this.orderPollingInterval);
                    this.orderPollingInterval = null;
                    console.log('已停止订单状态轮询');
                }
            },

            // 更新状态类映射
            getStatusClass(status) {
                switch(status) {
                    case 'pending_assignment':
                    case 'pending': return 'status-pending-meal';
                    case 'assigned': return 'status-assigned';
                    case 'delivering': return 'status-delivering';
                    case 'delivered': return 'status-delivered';
                    case 'completed': return 'status-completed';
                    default: return 'status-pending-meal';
                }
            },

            // 更新状态文本映射
            getStatusText(status) {
                switch(status) {
                    case 'pending_assignment':
                    case 'pending': return '待分配';
                    case 'assigned': return '已分配';
                    case 'delivering': return '配送中';
                    case 'delivered': return '已送达';
                    case 'completed': return '已完成';
                    default: return '待处理';
                }
            },

            // 获取工作人员端进度条步骤样式
            getWorkerStepClass(status, stepNumber) {
                const statusMap = {
                    'pending_assignment': 1,
                    'pending': 1,
                    'assigned': 2,
                    'delivering': 3,
                    'delivered': 4,
                    'completed': 5
                };

                const currentStep = statusMap[status] || 1;

                if (stepNumber < currentStep) {
                    return 'completed';
                } else if (stepNumber === currentStep) {
                    return 'active';
                } else {
                    return '';
                }
            }
        }
    });

    // 全局函数：设置快捷回复
    function setQuickResponse(message) {
        console.log('🔧 设置快捷回复:', message);

        // 更新textarea的值
        const textarea = document.getElementById('workerResponseMessage');
        if (textarea) {
            textarea.value = message;

            // 触发Vue的响应式更新
            const event = new Event('input', { bubbles: true });
            textarea.dispatchEvent(event);

            // 直接更新Vue实例的数据
            if (window.workerApp && window.workerApp.responseMessage !== undefined) {
                window.workerApp.responseMessage = message;
            }
        }
    }

    // 全局函数：直接提交工作人员响应
    function submitWorkerResponseDirect() {
        console.log('🚀 直接提交工作人员详细回复');

        const textarea = document.getElementById('workerResponseMessage');
        const responseMessage = textarea ? textarea.value.trim() : '';

        console.log('🚀 回复消息:', responseMessage);

        if (!responseMessage) {
            alert('请输入回复消息');
            return;
        }

        // 获取Vue实例
        const app = window.workerApp;
        if (!app) {
            console.error('❌ 无法获取Vue实例');
            alert('系统错误，请刷新页面重试');
            return;
        }

        if (!app.selectedEvent || !app.selectedEvent.event_id) {
            alert('未找到事件信息，请重试');
            console.error('❌ selectedEvent 或 event_id 为空');
            return;
        }

        // 调用Vue实例的方法
        app.respondToWorkerEventDirect(app.selectedEvent.event_id, responseMessage);
    }

    // 保存Vue实例到全局变量以便访问
    window.addEventListener('DOMContentLoaded', function() {
        // 等待Vue实例创建完成
        setTimeout(() => {
            const appElement = document.getElementById('worker-app');
            if (appElement && appElement.__vue__) {
                window.workerApp = appElement.__vue__;
                console.log('✅ Vue实例已保存到全局变量');
            }
        }, 1000);
    });
</script>
{% endblock %}