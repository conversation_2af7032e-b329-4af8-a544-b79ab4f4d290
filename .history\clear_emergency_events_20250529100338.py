#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
清空紧急事件数据脚本
用于清空数据库中的所有紧急事件记录
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'web_project'))

def clear_emergency_events():
    """清空所有紧急事件数据"""
    try:
        from web_project.app import create_app, db
        from web_project.app.models.models import EmergencyEvent
        
        app = create_app()
        
        with app.app_context():
            # 查询当前紧急事件数量
            current_count = EmergencyEvent.query.count()
            print(f"当前数据库中有 {current_count} 条紧急事件记录")
            
            if current_count == 0:
                print("数据库中没有紧急事件记录，无需清空")
                return
            
            # 删除所有紧急事件
            deleted_count = EmergencyEvent.query.delete()
            db.session.commit()
            
            print(f"✅ 成功清空所有紧急事件数据，共删除 {deleted_count} 条记录")
            
    except Exception as e:
        print(f"❌ 清空紧急事件失败: {str(e)}")
        if 'db' in locals():
            db.session.rollback()

if __name__ == "__main__":
    print("🧹 开始清空紧急事件数据...")
    clear_emergency_events()
    print("🎉 清空操作完成！")
