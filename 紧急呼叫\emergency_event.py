from flask import Blueprint, request, jsonify, current_app
import uuid
from datetime import datetime
from app import db
from app.models.models import Emergency<PERSON><PERSON>, <PERSON>ly<PERSON><PERSON>, FamilyUser, CommunityWorker

# 创建蓝图
emergency_event_bp = Blueprint('emergency_event_api', __name__)

@emergency_event_bp.route('/test', methods=['GET'])
def test_route():
    """测试路由，验证代码是否被加载"""
    print("🧪 测试路由被调用")
    return jsonify({'message': '测试路由正常工作', 'success': True}), 200

@emergency_event_bp.route('/create', methods=['POST'])
def create_emergency_event():
    """
    阶段一：老年端发起紧急呼叫
    接收数据: elderly_id, emergency_type, location, address, timestamp
    """
    try:
        data = request.json
        elderly_id = data.get('elderly_id')
        emergency_type = data.get('emergency_type')  # medical, fall, help
        location = data.get('location')  # GPS坐标 "lat,lng"
        address = data.get('address', '')  # 地址描述

        if not elderly_id or not emergency_type:
            return jsonify({'error': '缺少必要参数'}), 400

        # 检查老年人是否存在
        elderly = ElderlyUser.query.filter_by(user_id=elderly_id).first()
        if not elderly:
            return jsonify({'error': '未找到该老年人用户'}), 404

        # 生成事件ID
        event_id = 'EE' + datetime.now().strftime('%Y%m%d%H%M%S') + str(uuid.uuid4())[:4]

        # 创建紧急事件
        emergency_event = EmergencyEvent(
            event_id=event_id,
            elderly_id=elderly_id,
            emergency_type=emergency_type,
            location=location,
            address=address,
            status='待处理',
            family_responded=False,
            worker_responded=False
        )

        db.session.add(emergency_event)
        db.session.commit()

        return jsonify({
            'success': True,
            'message': '紧急事件已创建',
            'event_id': event_id,
            'timestamp': emergency_event.created_at.isoformat()
        }), 201

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': f'创建紧急事件失败: {str(e)}'}), 500

@emergency_event_bp.route('/active', methods=['GET'])
def get_active_events():
    """
    阶段二：获取未响应的紧急事件
    查询参数: user_type (family/worker), user_id
    """
    try:
        user_type = request.args.get('user_type')  # family 或 worker
        user_id = request.args.get('user_id')

        if not user_type or not user_id:
            return jsonify({'error': '缺少用户类型或用户ID'}), 400

        # 构建查询条件
        query = EmergencyEvent.query

        if user_type == 'family':
            # 家属可以看到绑定老人的所有事件（包括已处理的，用于统计）
            family_user = FamilyUser.query.filter_by(family_id=user_id).first()
            if not family_user:
                return jsonify({'error': '未找到家属用户'}), 404

            query = query.filter(
                EmergencyEvent.elderly_id == family_user.bound_elderly_id
            )

        elif user_type == 'worker':
            # 工作人员可以看到所有事件（包括已处理的，用于统计）
            # 不添加额外过滤条件，显示所有事件
            pass

        else:
            return jsonify({'error': '无效的用户类型'}), 400

        events = query.order_by(EmergencyEvent.created_at.desc()).all()

        result = []
        for event in events:
            elderly = ElderlyUser.query.filter_by(user_id=event.elderly_id).first()

            # 如果没有GPS位置信息，提供默认的杭州坐标
            location = event.location
            address = event.address

            if not location:
                # 为不同老人提供不同的模拟GPS坐标（杭州地区）
                elderly_locations = {
                    'E01': ('30.2741,120.1551', '浙江省杭州市余杭区龙湖天街1号'),
                    'E02': ('30.2500,120.1300', '浙江省杭州市西湖区西溪路518号'),
                    'E03': ('30.3000,120.1800', '浙江省杭州市拱墅区湖墅南路88号'),
                    'E04': ('30.2600,120.1700', '浙江省杭州市江干区钱江路1号'),
                    'E05': ('30.2800,120.1400', '浙江省杭州市下城区延安路1号')
                }

                if event.elderly_id in elderly_locations:
                    location, address = elderly_locations[event.elderly_id]
                else:
                    # 默认位置
                    location = '30.2741,120.1551'
                    address = address or '浙江省杭州市西湖区'

            result.append({
                'event_id': event.event_id,
                'elderly_id': event.elderly_id,
                'elderly_name': elderly.name if elderly else '未知',
                'emergency_type': event.emergency_type,
                'location': location,
                'address': address,
                'created_at': event.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                'status': event.status,
                'family_responded': event.family_responded,
                'worker_responded': event.worker_responded
            })

        return jsonify({'events': result}), 200

    except Exception as e:
        return jsonify({'error': f'获取事件失败: {str(e)}'}), 500

@emergency_event_bp.route('/respond', methods=['POST'])
def respond_to_event():
    """
    阶段三/四：家属或工作人员响应紧急事件
    接收数据: event_id, user_type (family/worker), user_id, response_note
    """
    print(f"🚀 响应API被调用")
    try:
        data = request.json
        print(f"📥 接收到数据: {data}")

        event_id = data.get('event_id')
        user_type = data.get('user_type')  # family 或 worker
        user_id = data.get('user_id')
        response_note = data.get('response_note', '')

        print(f"📋 解析参数: event_id={event_id}, user_type={user_type}, user_id={user_id}")

        if not event_id or not user_type or not user_id:
            print(f"❌ 缺少必要参数")
            return jsonify({'error': '缺少必要参数'}), 400

        # 查找事件
        print(f"🔍 查找事件: {event_id}")
        event = EmergencyEvent.query.filter_by(event_id=event_id).first()
        if not event:
            print(f"❌ 未找到事件")
            return jsonify({'error': '未找到该紧急事件'}), 404

        print(f"✅ 找到事件: {event.event_id}, 当前状态: {event.status}")

        # 验证用户权限
        if user_type == 'family':
            family_user = FamilyUser.query.filter_by(family_id=user_id).first()
            if not family_user or family_user.bound_elderly_id != event.elderly_id:
                return jsonify({'error': '无权限响应此事件'}), 403

            if event.family_responded:
                return jsonify({'error': '家属已响应此事件'}), 400

            # 标记家属已响应
            event.family_responded = True
            event.family_responder_id = user_id
            event.family_response_time = datetime.utcnow()
            event.family_response_note = response_note

        elif user_type == 'worker':
            worker = CommunityWorker.query.filter_by(worker_id=user_id).first()
            if not worker:
                return jsonify({'error': '未找到工作人员'}), 404

            if event.worker_responded:
                return jsonify({'error': '工作人员已响应此事件'}), 400

            # 标记工作人员已响应
            event.worker_responded = True
            event.worker_responder_id = user_id
            event.worker_response_time = datetime.utcnow()
            event.worker_response_note = response_note

        else:
            return jsonify({'error': '无效的用户类型'}), 400

        # 更新事件状态逻辑：
        # 家属响应后直接标记为"已处理"
        # 工作人员响应后也标记为"已处理"
        print(f"🔍 调试状态更新:")
        print(f"   事件ID: {event.event_id}")
        print(f"   当前状态: {event.status}")
        print(f"   家属已响应: {event.family_responded}")
        print(f"   工作人员已响应: {event.worker_responded}")

        if event.family_responded or event.worker_responded:
            old_status = event.status
            event.status = '已处理'
            print(f"   状态更新: {old_status} -> {event.status}")
        else:
            print(f"   状态更新条件不满足，保持: {event.status}")

        db.session.commit()
        print(f"   数据库提交后状态: {event.status}")

        return jsonify({
            'success': True,
            'message': f'{"家属" if user_type == "family" else "工作人员"}响应成功',
            'event_status': event.status
        }), 200

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': f'响应事件失败: {str(e)}'}), 500

@emergency_event_bp.route('/history/<elderly_id>', methods=['GET'])
def get_emergency_history(elderly_id):
    """获取老年人的紧急事件历史记录"""
    try:
        events = EmergencyEvent.query.filter_by(elderly_id=elderly_id).order_by(
            EmergencyEvent.created_at.desc()
        ).all()

        result = []
        for event in events:
            result.append({
                'event_id': event.event_id,
                'emergency_type': event.emergency_type,
                'location': event.location,
                'address': event.address,
                'created_at': event.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                'status': event.status,
                'family_responded': event.family_responded,
                'worker_responded': event.worker_responded,
                'family_response_time': event.family_response_time.strftime('%Y-%m-%d %H:%M:%S') if event.family_response_time else None,
                'worker_response_time': event.worker_response_time.strftime('%Y-%m-%d %H:%M:%S') if event.worker_response_time else None
            })

        return jsonify({'events': result}), 200

    except Exception as e:
        return jsonify({'error': f'获取历史记录失败: {str(e)}'}), 500

@emergency_event_bp.route('/notify_elderly', methods=['POST'])
def notify_elderly():
    """将家属和工作人员的响应信息通知给老人"""
    try:
        data = request.get_json()

        # 获取必要参数
        event_id = data.get('event_id')
        family_message = data.get('family_message', '')
        worker_message = data.get('worker_message', '')

        if not event_id:
            return jsonify({'error': '缺少事件ID'}), 400

        # 查找事件
        event = EmergencyEvent.query.filter_by(event_id=event_id).first()
        if not event:
            return jsonify({'error': '未找到该紧急事件'}), 404

        # 获取老人信息
        elderly = ElderlyUser.query.filter_by(user_id=event.elderly_id).first()
        if not elderly:
            return jsonify({'error': '未找到老人信息'}), 404

        # 构建通知消息
        notification_message = f"亲爱的{elderly.name}，您的紧急求助已收到响应：\n\n"

        if family_message:
            notification_message += f"👨‍👩‍👧‍👦 家属回复：{family_message}\n\n"

        if worker_message:
            notification_message += f"👷‍♂️ 工作人员回复：{worker_message}\n\n"

        notification_message += "请保持冷静，帮助正在路上！如有紧急情况请立即拨打120。"

        # 模拟发送通知（实际应用中可以通过短信、电话或APP推送）
        print(f"📱 发送通知给老人 {elderly.name} (ID: {elderly.user_id})")
        print(f"📞 老人电话: {elderly.phone}")
        print(f"📄 通知内容: {notification_message}")

        # 这里可以集成实际的通知服务
        # 例如：短信服务、电话服务、APP推送等

        return jsonify({
            'success': True,
            'message': f'通知已发送给{elderly.name}',
            'elderly_name': elderly.name,
            'elderly_phone': elderly.phone,
            'notification_content': notification_message
        }), 200

    except Exception as e:
        return jsonify({'error': f'发送通知失败: {str(e)}'}), 500

@emergency_event_bp.route('/clear_all', methods=['POST'])
def clear_all_events():
    """清空所有紧急事件数据"""
    try:
        # 删除所有紧急事件
        deleted_count = EmergencyEvent.query.delete()
        db.session.commit()

        return jsonify({
            'success': True,
            'message': f'已清空所有紧急事件数据，共删除 {deleted_count} 条记录'
        }), 200

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': f'清空紧急事件失败: {str(e)}'}), 500

@emergency_event_bp.route('/status/<event_id>', methods=['GET'])
def get_event_status(event_id):
    """获取单个紧急事件的详细状态信息"""
    try:
        # 查找事件
        event = EmergencyEvent.query.filter_by(event_id=event_id).first()
        if not event:
            return jsonify({'error': '未找到该紧急事件'}), 404

        # 获取老人信息
        elderly = ElderlyUser.query.filter_by(user_id=event.elderly_id).first()

        # 获取家属响应者信息
        family_responder_name = None
        if event.family_responder_id:
            family_user = FamilyUser.query.filter_by(family_id=event.family_responder_id).first()
            if family_user:
                family_responder_name = family_user.name

        # 获取工作人员响应者信息
        worker_responder_name = None
        if event.worker_responder_id:
            worker = CommunityWorker.query.filter_by(worker_id=event.worker_responder_id).first()
            if worker:
                worker_responder_name = worker.name

        # 如果没有GPS位置信息，提供默认的杭州坐标
        location = event.location
        address = event.address

        if not location:
            # 为不同老人提供不同的模拟GPS坐标（杭州地区）
            elderly_locations = {
                'E01': ('30.2741,120.1551', '浙江省杭州市余杭区龙湖天街1号'),
                'E02': ('30.2500,120.1300', '浙江省杭州市西湖区西溪路518号'),
                'E03': ('30.3000,120.1800', '浙江省杭州市拱墅区湖墅南路88号'),
                'E04': ('30.2600,120.1700', '浙江省杭州市江干区钱江路1号'),
                'E05': ('30.2800,120.1400', '浙江省杭州市下城区延安路1号')
            }

            if event.elderly_id in elderly_locations:
                location, address = elderly_locations[event.elderly_id]
            else:
                # 默认位置
                location = '30.2741,120.1551'
                address = address or '浙江省杭州市西湖区'

        result = {
            'event_id': event.event_id,
            'elderly_id': event.elderly_id,
            'elderly_name': elderly.name if elderly else '未知',
            'emergency_type': event.emergency_type,
            'location': location,
            'address': address,
            'created_at': event.created_at.strftime('%Y-%m-%d %H:%M:%S'),
            'status': event.status,
            'family_responded': event.family_responded,
            'worker_responded': event.worker_responded,
            'family_responder_name': family_responder_name,
            'worker_responder_name': worker_responder_name,
            'family_response_time': event.family_response_time.strftime('%Y-%m-%d %H:%M:%S') if event.family_response_time else None,
            'worker_response_time': event.worker_response_time.strftime('%Y-%m-%d %H:%M:%S') if event.worker_response_time else None,
            'family_response_note': event.family_response_note,
            'worker_response_note': event.worker_response_note
        }

        return jsonify({'success': True, 'event': result}), 200

    except Exception as e:
        return jsonify({'error': f'获取事件状态失败: {str(e)}'}), 500