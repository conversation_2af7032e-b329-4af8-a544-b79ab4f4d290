/* 紧急呼叫按钮样式 */
.emergency-call-btn {
    background-color: #FF4D4F;
    color: white;
    padding: 40px;
    border-radius: 12px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s;
    box-shadow: 0 4px 12px rgba(255, 77, 79, 0.3);
}

.emergency-call-btn:hover {
    background-color: #ff2b2e;
    transform: scale(1.05);
}

.emergency-call-btn i {
    font-size: 2.5rem;
    margin-bottom: 10px;
}

.floating-emergency-btn {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 1000;
    background-color: #dc3545;
    color: white;
    border-radius: 50%;
    width: 70px;
    height: 70px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 12px rgba(220, 53, 69, 0.4);
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
}

.floating-emergency-btn:hover {
    background-color: #c82333;
    transform: scale(1.1);
}

.floating-emergency-btn .floating-emergency-icon {
    font-size: 1.8rem; /* Adjusted icon size */
}

.floating-emergency-btn span {
    font-size: 0.7rem;
    margin-top: 2px;
}

/* 紧急呼叫模态框 spécifiques */
#emergencyModal .modal-header {
    border-bottom: none;
}

#emergencyModal .modal-body {
    padding: 2rem;
}

#emergencyModal .emergency-option {
    border: 2px solid #e9ecef;
    border-radius: 10px;
    transition: all 0.2s ease-in-out;
}

#emergencyModal .emergency-option:hover, #emergencyModal .emergency-option.active {
    border-color: #dc3545;
    background-color: rgba(220, 53, 69, 0.1);
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(220,53,69,0.2);
}

#emergencyModal .emergency-option i, #emergencyModal .emergency-option img {
    font-size: 3rem; /* Increased icon size */
    margin-bottom: 0.75rem;
    color: #dc3545;
}

#emergencyModal .emergency-option.medical i { color: #0d6efd; }
#emergencyModal .emergency-option.fall img { /* Fall icon is an image, no color needed here */ }
#emergencyModal .emergency-option.help i { color: #198754; }

#emergencyModal .emergency-option.medical:hover, #emergencyModal .emergency-option.medical.active { border-color: #0d6efd; background-color: rgba(13,110,253,0.1); box-shadow: 0 5px 15px rgba(13,110,253,0.2);}
#emergencyModal .emergency-option.fall:hover, #emergencyModal .emergency-option.fall.active { border-color: #ffc107; background-color: rgba(255,193,7,0.1); box-shadow: 0 5px 15px rgba(255,193,7,0.2);}
#emergencyModal .emergency-option.help:hover, #emergencyModal .emergency-option.help.active { border-color: #198754; background-color: rgba(25,135,84,0.1); box-shadow: 0 5px 15px rgba(25,135,84,0.2);}


#emergencyModal .emergency-countdown {
    font-size: 4rem;
    font-weight: bold;
    color: #dc3545;
    width: 150px;
    height: 150px;
    border-radius: 50%;
    border: 8px solid #dc3545;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem auto; /* Centered and spacing */
    animation: pulse-animation 1.5s infinite;
}

@keyframes pulse-animation {
    0% { transform: scale(1); box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.7); }
    50% { transform: scale(1.05); box-shadow: 0 0 0 15px rgba(220, 53, 69, 0); }
    100% { transform: scale(1); box-shadow: 0 0 0 0 rgba(220, 53, 69, 0); }
}

#emergencyModal .emergency-map-container {
    height: 250px; /* Adjust as needed */
    background-color: #e9ecef;
    border-radius: 8px;
    margin-bottom: 1rem;
}

#emergencyModal .location-info {
    display: flex;
    align-items: center;
    background-color: #f8f9fa;
    padding: 0.75rem 1rem;
    border-radius: 8px;
    margin-bottom: 1rem;
}

#emergencyModal .location-info i {
    font-size: 1.5rem;
    color: #0d6efd;
    margin-right: 0.75rem;
}

#emergencyModal .emergency-status-progress {
    height: 10px;
    border-radius: 5px;
    overflow: hidden;
    background-color: #e9ecef;
    margin-bottom: 1.5rem;
}

#emergencyModal .emergency-status-progress .progress-bar {
    background-color: #28a745; /* Green for progress */
}

/* 横向时间轴样式 */
.horizontal-timeline {
    margin: 20px 0;
}

.timeline-container {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    position: relative;
    padding: 20px 0;
    overflow-x: auto;
}

.timeline-container::before {
    content: '';
    position: absolute;
    top: 50px;
    left: 10%;
    right: 10%;
    height: 3px;
    background: linear-gradient(to right, #28a745 0%, #28a745 20%, #ffc107 20%, #ffc107 80%, #e9ecef 80%, #e9ecef 100%);
    z-index: 1;
}

.timeline-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    position: relative;
    flex: 1;
    min-width: 120px;
    z-index: 2;
}

.timeline-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 10px;
    font-size: 1.5rem;
    border: 3px solid;
    background-color: white;
    transition: all 0.3s ease;
}

.timeline-step.completed .timeline-icon {
    background-color: #28a745;
    border-color: #28a745;
    color: white;
    animation: completedPulse 2s infinite;
}

.timeline-step.active .timeline-icon {
    background-color: #ffc107;
    border-color: #ffc107;
    color: white;
    animation: activePulse 1.5s infinite;
}

.timeline-step.pending .timeline-icon {
    background-color: #f8f9fa;
    border-color: #e9ecef;
    color: #6c757d;
}

.timeline-content {
    max-width: 120px;
}

.timeline-title {
    font-weight: 600;
    font-size: 0.9rem;
    margin-bottom: 5px;
    color: #333;
}

.timeline-time {
    font-size: 0.8rem;
    color: #666;
    margin-bottom: 3px;
}

.timeline-responder {
    font-size: 0.75rem;
    color: #28a745;
    font-weight: 500;
}

/* 动画效果 */
@keyframes completedPulse {
    0% {
        box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(40, 167, 69, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(40, 167, 69, 0);
    }
}

@keyframes activePulse {
    0% {
        box-shadow: 0 0 0 0 rgba(255, 193, 7, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(255, 193, 7, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(255, 193, 7, 0);
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .timeline-container {
        flex-direction: column;
        align-items: center;
    }

    .timeline-container::before {
        display: none;
    }

    .timeline-step {
        margin-bottom: 20px;
        width: 100%;
        max-width: 200px;
    }

    .timeline-content {
        max-width: 200px;
    }
}