#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
清空紧急事件数据脚本
用于清空数据库中的所有紧急事件记录
"""

import mysql.connector
from datetime import datetime

def clear_emergency_events():
    """清空所有紧急事件数据"""
    try:
        # 连接数据库
        connection = mysql.connector.connect(
            host='localhost',
            user='root',
            password='123456',
            database='elderly_care_system'
        )

        cursor = connection.cursor()

        # 查询当前紧急事件数量
        cursor.execute("SELECT COUNT(*) FROM emergencyevent")
        current_count = cursor.fetchone()[0]
        print(f"当前数据库中有 {current_count} 条紧急事件记录")

        if current_count == 0:
            print("数据库中没有紧急事件记录，无需清空")
            return

        # 删除所有紧急事件
        cursor.execute("DELETE FROM emergencyevent")
        connection.commit()

        print(f"✅ 成功清空所有紧急事件数据，共删除 {current_count} 条记录")

    except mysql.connector.Error as e:
        print(f"❌ 数据库操作失败: {str(e)}")
        if connection:
            connection.rollback()
    except Exception as e:
        print(f"❌ 清空紧急事件失败: {str(e)}")
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'connection' in locals():
            connection.close()

if __name__ == "__main__":
    print("🧹 开始清空紧急事件数据...")
    clear_emergency_events()
    print("🎉 清空操作完成！")
